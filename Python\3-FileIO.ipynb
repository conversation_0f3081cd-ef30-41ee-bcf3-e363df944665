{"cells": [{"cell_type": "markdown", "id": "1128843a", "metadata": {}, "source": ["# Module 3: Part 2"]}, {"cell_type": "code", "execution_count": 6, "id": "790ccef3-920e-435e-8035-dce23966f5a5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Overwriting myfile.txt\n"]}], "source": ["%%writefile myfile.txt\n", "Hello this is a text file\n", "This in 2nd Line"]}, {"cell_type": "code", "execution_count": 7, "id": "d4b8ff8a-e4f6-4aec-a0ed-da70941cd16f", "metadata": {}, "outputs": [], "source": ["myfile = open('myfile.txt')"]}, {"cell_type": "code", "execution_count": null, "id": "b2ddd387-edcb-426c-bf58-fdfd7bcf1ea1", "metadata": {}, "outputs": [{"data": {"text/plain": ["'C:\\\\Users\\\\<USER>\\\\training-crest\\\\Python'"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["pwd"]}, {"cell_type": "code", "execution_count": 9, "id": "d1b0cdc5-7a82-43c0-929d-ea335659cd10", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Hello this is a text file\\nThis in 2nd Line\\n'"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["myfile.read()"]}, {"cell_type": "code", "execution_count": 10, "id": "a1ccbef1-b6bf-43b1-a8ce-773b5027782c", "metadata": {}, "outputs": [{"data": {"text/plain": ["''"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["myfile.read() # it will print nothing as cursor is at the end of the file and there's nothing to read"]}, {"cell_type": "code", "execution_count": 18, "id": "c55c61e9-2b35-4841-b491-a418e27d0d65", "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["myfile.seek(0) #used to change position of cursor"]}, {"cell_type": "code", "execution_count": 12, "id": "fe394c11-4ebf-4441-8e2e-ec85c9eabd92", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Hello this is a text file\\nThis in 2nd Line\\n'"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["myfile.read()"]}, {"cell_type": "code", "execution_count": 16, "id": "4edd3300-715f-413b-a534-daae4cfcca58", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Hello this is a text file\\nThis in 2nd Line\\n'"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["contents = myfile.read()\n", "contents"]}, {"cell_type": "code", "execution_count": 19, "id": "2214295e-52fe-48c8-8134-3147dbf8625f", "metadata": {}, "outputs": [{"data": {"text/plain": ["['Hello this is a text file\\n', 'This in 2nd Line\\n']"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["myfile.readlines()"]}, {"cell_type": "markdown", "id": "4c330b4f-7402-40e7-a96d-0b1f6b104e07", "metadata": {}, "source": ["**To open a file outside the current directory, you must provide the full path.**"]}, {"cell_type": "code", "execution_count": 21, "id": "389dd49c-adb6-40ce-907f-76caf0bf87f4", "metadata": {}, "outputs": [], "source": ["myfile.close()"]}, {"cell_type": "code", "execution_count": 22, "id": "5dde4296-3c24-48a2-9ca6-4f5b3d9d42a5", "metadata": {}, "outputs": [], "source": ["with  open('myfile.txt') as my_file:\n", "    contents = my_file.read()"]}, {"cell_type": "code", "execution_count": 23, "id": "51f2501f-af17-4321-abae-00035a4d9201", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Hello this is a text file\\nThis in 2nd Line\\n'"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["contents"]}, {"cell_type": "code", "execution_count": 25, "id": "0ec238b8-3ef5-46dc-87a9-b85298dc7488", "metadata": {}, "outputs": [], "source": ["with  open('myfile.txt',mode='r') as my_file:\n", "    contents = my_file.read()"]}, {"cell_type": "markdown", "id": "182bbfdd-c8bd-4f14-87b6-82b1696c6739", "metadata": {}, "source": ["## Reading, Writing, Appending Modes\n", "\n", "- **mode='r'** is read only\n", "- **mode='w'** is write only (will overwrite files or create new!)\n", "- **mode='a'** is append only (will add on to files)\n", "- **mode='r+'** is reading and writing\n", "- **mode='w+'** is writing and reading (Overwrites existing files or creates a new file!)\n"]}, {"cell_type": "code", "execution_count": 27, "id": "58c0f3c6-75c0-44de-944e-80871be2aa3b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Overwriting demo.txt\n"]}], "source": ["%%writefile demo.txt\n", "One on First\n", "Two on Second\n", "Three on Third"]}, {"cell_type": "code", "execution_count": 28, "id": "219a6fd2-c320-4ce8-97d3-51bb61debe67", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["One on First\n", "Two on Second\n", "Three on Third\n", "\n"]}], "source": ["with  open('demo.txt',mode='r') as f:\n", "    print(f.read())"]}, {"cell_type": "code", "execution_count": 29, "id": "53d4a45e-74ea-4b70-abf4-696970c7a5ac", "metadata": {}, "outputs": [], "source": ["with  open('demo.txt',mode='a') as f:\n", "    f.write(\"Four on Fourth\")"]}, {"cell_type": "code", "execution_count": 30, "id": "b104f486-e710-4555-ab6b-e2e7fbf57fef", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["One on First\n", "Two on Second\n", "Three on Third\n", "Four on Fourth\n"]}], "source": ["with  open('demo.txt',mode='r') as f:\n", "    print(f.read())"]}, {"cell_type": "code", "execution_count": 31, "id": "a9586785-7e44-48e6-8d3f-34b104a642ae", "metadata": {}, "outputs": [], "source": ["with  open('abc.txt',mode='w') as f:\n", "    f.write('I created this file')"]}, {"cell_type": "code", "execution_count": 32, "id": "42b25514-9aaf-43dc-963d-f58a63ce5588", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["I created this file\n"]}], "source": ["with  open('abc.txt',mode='r') as f:\n", "    print(f.read())"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}