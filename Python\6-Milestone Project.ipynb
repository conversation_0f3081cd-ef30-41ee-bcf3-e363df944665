{"cells": [{"cell_type": "markdown", "id": "d45bb3d2", "metadata": {}, "source": ["# Module 7: Mile Stone project"]}, {"cell_type": "markdown", "id": "e231f155", "metadata": {}, "source": ["## <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": 24, "id": "ccc952f6", "metadata": {}, "outputs": [], "source": ["def display(row1, row2, row3):\n", "    print(row1)\n", "    print(row2)\n", "    print(row3)"]}, {"cell_type": "code", "execution_count": 25, "id": "afcb1176", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[' ', ' ', ' ']\n", "[' ', ' ', ' ']\n", "[' ', ' ', ' ']\n"]}], "source": ["row1 = [' ', ' ', ' ']\n", "row2 = [' ', ' ', ' ']\n", "row3 = [' ', ' ', ' ']\n", "\n", "display(row1, row2, row3)"]}, {"cell_type": "code", "execution_count": 26, "id": "99c822b4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[' ', ' ', ' ']\n", "[' ', 'X', ' ']\n", "[' ', ' ', ' ']\n"]}], "source": ["row2[1] = 'X'\n", "display(row1, row2, row3)"]}, {"cell_type": "code", "execution_count": 27, "id": "02c22765", "metadata": {}, "outputs": [], "source": ["from IPython.display import clear_output\n", "\n", "def display_board(board):\n", "    clear_output()  # Remember, this only works in jupyter!\n", "    \n", "    print('   |   |')\n", "    print(' ' + board[7] + ' | ' + board[8] + ' | ' + board[9])\n", "    print('   |   |')\n", "    print('-----------')\n", "    print('   |   |')\n", "    print(' ' + board[4] + ' | ' + board[5] + ' | ' + board[6])\n", "    print('   |   |')\n", "    print('-----------')\n", "    print('   |   |')\n", "    print(' ' + board[1] + ' | ' + board[2] + ' | ' + board[3])\n", "    print('   |   |')"]}, {"cell_type": "code", "execution_count": 28, "id": "40883d31", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   |   |\n", " X | O | X\n", "   |   |\n", "-----------\n", "   |   |\n", " O | X | O\n", "   |   |\n", "-----------\n", "   |   |\n", " X | O | X\n", "   |   |\n"]}], "source": ["test_board = ['#','X','O','X','O','X','O','X','O','X']\n", "display_board(test_board)"]}, {"cell_type": "code", "execution_count": 29, "id": "2742d828", "metadata": {}, "outputs": [], "source": ["def player_input():\n", "    marker = ''\n", "    \n", "    while not (marker == 'X' or marker == 'O'):\n", "        marker = input('Player 1: Do you want to be X or O? ').upper()\n", "\n", "    if marker == 'X':\n", "        return ('X', 'O')\n", "    else:\n", "        return ('O', 'X')"]}, {"cell_type": "code", "execution_count": 30, "id": "f7f69ca8", "metadata": {}, "outputs": [{"data": {"text/plain": ["('X', 'O')"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["player_input()"]}, {"cell_type": "code", "execution_count": 31, "id": "01add5a3", "metadata": {}, "outputs": [], "source": ["def place_marker(board, marker, position):\n", "    board[position] = marker"]}, {"cell_type": "code", "execution_count": 32, "id": "69aca4a5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   |   |\n", " X | $ | X\n", "   |   |\n", "-----------\n", "   |   |\n", " O | X | O\n", "   |   |\n", "-----------\n", "   |   |\n", " X | O | X\n", "   |   |\n"]}], "source": ["place_marker(test_board, '$', 8)\n", "display_board(test_board)"]}, {"cell_type": "code", "execution_count": 33, "id": "20adb48b", "metadata": {}, "outputs": [], "source": ["def win_check(board, mark):\n", "\n", "    return ((board[7] == mark and board[8] == mark and board[9] == mark) or # across the top\n", "    (board[4] == mark and board[5] == mark and board[6] == mark) or # across the middle\n", "    (board[1] == mark and board[2] == mark and board[3] == mark) or # across the bottom\n", "    (board[7] == mark and board[4] == mark and board[1] == mark) or # down the middle\n", "    (board[8] == mark and board[5] == mark and board[2] == mark) or # down the middle\n", "    (board[9] == mark and board[6] == mark and board[3] == mark) or # down the right side\n", "    (board[7] == mark and board[5] == mark and board[3] == mark) or # diagonal\n", "    (board[9] == mark and board[5] == mark and board[1] == mark)) # diagonal\n"]}, {"cell_type": "code", "execution_count": 34, "id": "8310fbdd", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["win_check(test_board, 'X')\n"]}, {"cell_type": "code", "execution_count": 35, "id": "24f9d88e", "metadata": {}, "outputs": [], "source": ["import random\n", "\n", "def choose_first():\n", "    if random.randint(0, 1) == 0:\n", "        return 'Player 2'\n", "    else:\n", "        return 'Player 1'"]}, {"cell_type": "code", "execution_count": 36, "id": "ecbcbdc6", "metadata": {}, "outputs": [], "source": ["def space_check(board, position):\n", "    return board[position] == ' '"]}, {"cell_type": "code", "execution_count": 37, "id": "759414ee", "metadata": {}, "outputs": [], "source": ["def full_board_check(board):\n", "    for i in range(1,10):\n", "        if space_check(board, i):\n", "            return False\n", "    return True"]}, {"cell_type": "code", "execution_count": 38, "id": "c572b5cc", "metadata": {}, "outputs": [], "source": ["def player_choice(board):\n", "    position = 0\n", "    while position not in [1, 2, 3, 4, 5, 6, 7, 8, 9] or not space_check(board, position):\n", "        position = int(input(f'Choose your next position: (1-9) '))\n", "    return position"]}, {"cell_type": "code", "execution_count": 39, "id": "004dff4c", "metadata": {}, "outputs": [], "source": ["def replay():\n", "    return input('Do you want to play again? Enter Yes or No: ').lower().startswith('y')"]}, {"cell_type": "code", "execution_count": 40, "id": "282dd574", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   |   |\n", " O | X |  \n", "   |   |\n", "-----------\n", "   |   |\n", "   | X |  \n", "   |   |\n", "-----------\n", "   |   |\n", " O | X | O\n", "   |   |\n", "Congratulations! You have won the game!\n"]}], "source": ["print(\"Welcome to <PERSON>ic <PERSON>c <PERSON>!\")\n", "\n", "while True:\n", "    theBoard = [' '] * 10\n", "    player1_marker, player2_marker = player_input()\n", "    turn = choose_first()\n", "    print(turn + ' will go first.')\n", "    \n", "    play_game = input('Are you ready to play? Enter Yes or No.')\n", "    \n", "    if play_game.lower()[0] == 'y':\n", "        game_on = True\n", "    else:\n", "        game_on = False\n", "\n", "    while game_on:\n", "        if turn == 'Player 1':\n", "            # Player1's turn.\n", "            \n", "            display_board(theBoard)\n", "            position = player_choice(theBoard)\n", "            place_marker(theBoard, player1_marker, position)\n", "\n", "            if win_check(theBoard, player1_marker):\n", "                display_board(theBoard)\n", "                print('Congratulations! You have won the game!')\n", "                game_on = False\n", "            else:\n", "                if full_board_check(theBoard):\n", "                    display_board(theBoard)\n", "                    print('The game is a draw!')\n", "                    break\n", "                else:\n", "                    turn = 'Player 2'\n", "\n", "        else:\n", "            # Player2's turn.\n", "            \n", "            display_board(theBoard)\n", "            position = player_choice(theBoard)\n", "            place_marker(theBoard, player2_marker, position)\n", "\n", "            if win_check(theBoard, player2_marker):\n", "                display_board(theBoard)\n", "                print('Player 2 has won!')\n", "                game_on = False\n", "            else:\n", "               if full_board_check(theBoard):\n", "                    display_board(theBoard)\n", "                    print('The game is a draw!')\n", "                    break\n", "               else:\n", "                    turn = 'Player 1'\n", "\n", "    if not replay():\n", "        break"]}, {"cell_type": "code", "execution_count": null, "id": "64600e29", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}