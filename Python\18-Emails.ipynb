{"cells": [{"cell_type": "markdown", "id": "c2304067-b76d-42ce-ad41-c15b8aef3998", "metadata": {}, "source": ["# Module 18: Emails with Python"]}, {"cell_type": "markdown", "id": "78141925-3407-4ab0-bd51-75e10e01f14e", "metadata": {}, "source": ["**Sending Emails with <PERSON>**\n", "\n", "Python allows you to send emails programmatically using the built-in `smtplib` and `email` modules.\n", "\n", "- **`smtplib`**: Handles the connection to the SMTP server for sending emails.\n", "- **`email` module**: Used to construct well-formatted email messages, including plain text, HTML, and attachments.\n", "- **Authentication**: Most email providers (like Gmail) require login credentials and secure connections (TLS or SSL).\n", "- **SMTP Servers**: You must connect to an SMTP server (e.g., `smtp.gmail.com`) and provide the correct port (usually 587 for TLS).\n", "- **Use Cases**: Automated alerts, newsletters, report delivery, and notifications.\n", "\n", "This functionality is useful for integrating email capabilities into scripts and applications.\n"]}, {"cell_type": "markdown", "id": "4b3bb5ca-4a55-4a5e-bed5-6e87b460b1c9", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["## Sending Emails"]}, {"cell_type": "code", "execution_count": 1, "id": "9c1e92a4-722e-4d5d-981b-a7a4288835bf", "metadata": {}, "outputs": [], "source": ["import smtplib"]}, {"cell_type": "code", "execution_count": 3, "id": "6fd7053a-bbbb-4ddb-aee7-73cbe869169d", "metadata": {}, "outputs": [], "source": ["smtp_object = smtplib.SMTP('smtp.gmail.com',587)"]}, {"cell_type": "code", "execution_count": 4, "id": "f14c1d83-ba68-4c6f-8880-7dc547206270", "metadata": {}, "outputs": [{"data": {"text/plain": ["(250,\n", " b'smtp.gmail.com at your service, [***************]\\nSIZE 36700160\\n8BITMIME\\nSTARTTLS\\nENHANCEDSTATUSCODES\\nPIPELINING\\nCHUNKING\\nSMTPUTF8')"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["smtp_object.ehlo()"]}, {"cell_type": "code", "execution_count": 6, "id": "a06ae715-66b1-4472-a533-e6a29fe6e5cf", "metadata": {}, "outputs": [{"data": {"text/plain": ["(220, b'2.0.0 Ready to start TLS')"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["smtp_object.starttls()"]}, {"cell_type": "code", "execution_count": 7, "id": "8a7205d0-3614-4d6f-8467-fc497f934ad8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["What is your Password?:  visible\n"]}], "source": ["password = input('What is your Password?: ')  # Don't use these method as password is visible to everyone, Instead use getpass library"]}, {"cell_type": "code", "execution_count": 8, "id": "99bf6443-ba8e-4cdf-812f-b5b9cd5d1150", "metadata": {}, "outputs": [], "source": ["import getpass"]}, {"cell_type": "code", "execution_count": 11, "id": "1c16b26d-200d-447c-8692-c0390fc3904c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Password please:  ········\n"]}], "source": ["password = getpass.getpass('Password please: ')"]}, {"cell_type": "code", "execution_count": null, "id": "d04cf96e-7ed9-47d4-9bfe-d7c648c92fdf", "metadata": {}, "outputs": [], "source": ["email = getpass.getpass('Email: ')\n", "password = getpass.getpass('Password: ')\n", "smtp_object.login(email,password)"]}, {"cell_type": "code", "execution_count": null, "id": "95a200d4-ccbd-4adf-8d38-b92114aa0a4c", "metadata": {}, "outputs": [], "source": ["from_address = email\n", "to_address = email\n", "subject = input(\"Enter the subject line: \")\n", "message = input(\"Enter the body message: \")\n", "msg = \"Subject: \"+subject+'\\n'+message\n", "\n", "smtp_object.sendmail(from_address,to_address,msg)"]}, {"cell_type": "code", "execution_count": 15, "id": "f300a69e-b6c8-4f2a-a533-b90fb7647234", "metadata": {}, "outputs": [{"data": {"text/plain": ["(221,\n", " b'2.0.0 closing connection d9443c01a7336-23de4322e23sm103258825ad.104 - gsmtp')"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["smtp_object.quit()"]}, {"cell_type": "markdown", "id": "1c9282fc-5844-4948-a49b-7bb91be94ac5", "metadata": {}, "source": ["## Receiving Emails"]}, {"cell_type": "markdown", "id": "84c9f219-68e2-42c8-90e2-081f540399b9", "metadata": {}, "source": ["**Receiving Emails with <PERSON>**\n", "\n", "Python can receive and process emails using built-in libraries and third-party packages.\n", "\n", "- **`imaplib`**: Allows you to connect to an IMAP server to fetch and manage emails.\n", "- **`poplib`**: Provides access to emails using the POP3 protocol.\n", "- **Authentication**: Requires login credentials to connect securely to the mail server.\n", "- **Email Parsing**: Use the `email` module to parse raw email content into readable messages and attachments.\n", "- **Use Cases**: Automating inbox processing, extracting attachments, filtering emails, and building custom mail clients.\n", "\n", "Receiving emails is commonly used for workflows like monitoring incoming messages or downloading reports.\n"]}, {"cell_type": "code", "execution_count": 16, "id": "9f6194fe-6407-407f-ac7d-998bab69ed56", "metadata": {}, "outputs": [], "source": ["import imaplib"]}, {"cell_type": "code", "execution_count": 17, "id": "fd31c7c0-d10c-4625-9654-6cfd0553ae38", "metadata": {}, "outputs": [], "source": ["M = imaplib.IMAP4_SSL('imap.gmail.com')"]}, {"cell_type": "code", "execution_count": 18, "id": "1468dba4-843c-492b-9ea2-0cffee8468ba", "metadata": {}, "outputs": [], "source": ["import getpass"]}, {"cell_type": "code", "execution_count": null, "id": "5ea0a507-5369-490f-b229-3c85e0cc4732", "metadata": {}, "outputs": [], "source": ["email = getpass.getpass(\"Email: \")\n", "password = getpass.getpass(\"Password: \")"]}, {"cell_type": "code", "execution_count": null, "id": "b2455bea-9915-458e-a294-acb84da08173", "metadata": {}, "outputs": [], "source": ["<PERSON><PERSON>login(email,password)"]}, {"cell_type": "code", "execution_count": null, "id": "b68ffdd9-9e29-4175-93a3-2787b1c47294", "metadata": {}, "outputs": [], "source": ["<PERSON>.list()"]}, {"cell_type": "code", "execution_count": null, "id": "609653f2-0c25-4313-a3b1-9a965bf971c1", "metadata": {}, "outputs": [], "source": ["<PERSON><PERSON>select('inbox')"]}, {"cell_type": "code", "execution_count": null, "id": "f679f15f-9bf9-4f85-8062-e41ca80f1ff6", "metadata": {}, "outputs": [], "source": ["typ,data = M.search(None, 'SUBJECT \"NEW TEST PYTHON\"')"]}, {"cell_type": "code", "execution_count": null, "id": "821b4cd7-0603-4922-a1af-6202908815ca", "metadata": {}, "outputs": [], "source": ["typ"]}, {"cell_type": "code", "execution_count": null, "id": "1910bc5a-9b9a-4ced-8152-8c64b8371fd4", "metadata": {}, "outputs": [], "source": ["email_id = data[0]"]}, {"cell_type": "code", "execution_count": null, "id": "7a39cdb1-56c0-4202-88ff-4aff755a467b", "metadata": {}, "outputs": [], "source": ["result, email_data = M.fetch(email_id,'(RFC822)')"]}, {"cell_type": "code", "execution_count": null, "id": "5714871c-da02-4671-94e2-bd2f0bc43a1b", "metadata": {}, "outputs": [], "source": ["# email_data"]}, {"cell_type": "code", "execution_count": null, "id": "74942df7-6576-493e-888d-2b1a14a9bb66", "metadata": {}, "outputs": [], "source": ["raw_email = email_data[0][1]"]}, {"cell_type": "code", "execution_count": null, "id": "25359b32-a94c-45c7-a2c6-43948120e5ea", "metadata": {}, "outputs": [], "source": ["raw_email_string = raw_email.decode('utf-8')"]}, {"cell_type": "code", "execution_count": 20, "id": "6dff1dfb-cb4f-404e-8225-68d5036394a6", "metadata": {}, "outputs": [], "source": ["import email"]}, {"cell_type": "code", "execution_count": null, "id": "bbb58ba3-f41b-4637-8838-8ca338fdec4d", "metadata": {}, "outputs": [], "source": ["email_message = email.message_from_string(raw_email_string)"]}, {"cell_type": "code", "execution_count": null, "id": "a73fa531-1c3e-4e6f-aad7-983982de840b", "metadata": {}, "outputs": [], "source": ["for part in email_message.walk():\n", "    if part.get_content_type() == 'text/plain':\n", "        body = part.get_payload(decode=True)\n", "        print(body)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}