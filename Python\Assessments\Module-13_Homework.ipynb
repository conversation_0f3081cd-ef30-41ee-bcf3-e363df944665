{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Generators Homework \n", "\n", "### Problem 1\n", "\n", "Create a generator that generates the squares of numbers up to some number N."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["def gens<PERSON>res(N):\n", "    for i in range(1,N+1):\n", "        yield i"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n", "2\n", "3\n", "4\n", "5\n", "6\n", "7\n", "8\n", "9\n", "10\n"]}], "source": ["for x in gensquares(10):\n", "    print(x)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Problem 2\n", "\n", "Create a generator that yields \"n\" random numbers between a low and high number (that are inputs). <br>Note: Use the random library. For example:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["9"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["import random\n", "\n", "random.ran<PERSON>t(1,10)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["def rand_num(low,high,n):\n", "    for i in range(n):\n", "        yield random.randint(low,high)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["9\n", "8\n", "8\n", "8\n", "1\n", "4\n", "5\n", "1\n", "4\n", "8\n", "6\n", "5\n"]}], "source": ["for num in rand_num(1,10,12):\n", "    print(num)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Problem 3\n", "\n", "Use the iter() function to convert the string below into an iterator:\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["h\n", "e\n", "l\n", "l\n", "o\n"]}], "source": ["s = 'hello'\n", "a = iter(s)\n", "print(next(a))\n", "print(next(a))\n", "print(next(a))\n", "print(next(a))\n", "print(next(a))\n", "\n", "#code here"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Problem 4\n", "Explain a use case for a generator using a yield statement where you would not want to use a normal function with a return statement.<br><br><br><br><br><br>\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Extra Credit!\n", "Can you explain what *gencomp* is in the code below? (Note: We never covered this in lecture! You will have to do some Googling/Stack Overflowing!)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["4\n", "5\n"]}], "source": ["my_list = [1,2,3,4,5]\n", "\n", "gencomp = (item for item in my_list if item > 3)\n", "\n", "for item in gencomp:\n", "    print(item)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Hint: Google *generator comprehension*!\n", "\n", "# Great Job!"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Filtered List (List Comprehension): [5, 9, 6]\n", "Length of Filtered List: 3\n", "Filtered Generator: <generator object <genexpr> at 0x0000027BE2F25B10>\n", "Error: object of type 'generator' has no len()\n", "Next item from generator: 5\n", "Next item from generator: 9\n", "Next item from generator: 6\n", "Generator is exhausted (StopIteration).\n", "List from Generator: [5, 9, 6]\n", "List Comprehension equals Generator List: True\n"]}], "source": ["# Define the original list\n", "my_list = [1, 3, 5, 9, 2, 6]\n", "\n", "# Using list comprehension to filter items greater than 3\n", "filtered_list = [item for item in my_list if item > 3]\n", "print(\"Filtered List (List Comprehension):\", filtered_list)\n", "print(\"Length of Filtered List:\", len(filtered_list))\n", "\n", "# Using generator expression to filter items greater than 3\n", "filtered_gen = (item for item in my_list if item > 3)\n", "print(\"Filtered Generator:\", filtered_gen)  # This prints the generator object\n", "\n", "# Demonstrating that a generator has no length\n", "try:\n", "    print(\"Length of Generator:\", len(filtered_gen))\n", "except TypeError as e:\n", "    print(\"Error:\", e)\n", "\n", "# Extracting items manually using next()\n", "filtered_gen = (item for item in my_list if item > 3)  # re-initialize\n", "try:\n", "    print(\"Next item from generator:\", next(filtered_gen))\n", "    print(\"Next item from generator:\", next(filtered_gen))\n", "    print(\"Next item from generator:\", next(filtered_gen))\n", "    print(\"Next item from generator:\", next(filtered_gen))  # Should raise StopIteration\n", "except StopIteration:\n", "    print(\"Generator is exhausted (StopIteration).\")\n", "\n", "# Proving that generator yields the same results as list comprehension\n", "filtered_gen = (item for item in my_list if item > 3)\n", "gen_to_list = list(filtered_gen)\n", "print(\"List from Generator:\", gen_to_list)\n", "print(\"List Comprehension equals Generator List:\", filtered_list == gen_to_list)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 4}