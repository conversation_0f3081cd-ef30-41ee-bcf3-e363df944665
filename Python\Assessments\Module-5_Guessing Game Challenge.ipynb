{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["___\n", "\n", "<a href='https://www.udemy.com/user/joseportilla/'><img src='../<PERSON>ian_Data_Logo.png'/></a>\n", "___\n", "<center><em>Content Copyright by Pierian Data</em></center>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Guessing Game Challenge\n", "\n", "Let's use `while` loops to create a guessing game.\n", "\n", "The Challenge:\n", "\n", "Write a program that picks a random integer from 1 to 100, and has players guess the number. The rules are:\n", "\n", "1. If a player's guess is less than 1 or greater than 100, say \"OUT OF BOUNDS\"\n", "2. On a player's first turn, if their guess is\n", " * within 10 of the number, return \"WARM!\"\n", " * further than 10 away from the number, return \"COLD!\"\n", "3. On all subsequent turns, if a guess is \n", " * closer to the number than the previous guess return \"WARMER!\"\n", " * farther from the number than the previous guess, return \"COLDER!\"\n", "4. When the player's guess equals the number, tell them they've guessed correctly *and* how many guesses it took!\n", "\n", "You can try this from scratch, or follow the steps outlined below. A separate Solution notebook has been provided. Good luck!\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### First, pick a random integer from 1 to 100 using the random module and assign it to a variable\n", "\n", "Note: `random.randint(a,b)` returns a random integer in range `[a, b]`, including both end points."]}, {"cell_type": "code", "execution_count": 8, "metadata": {"collapsed": true}, "outputs": [{"data": {"text/plain": ["62"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["import random\n", "\n", "num = random.randint(1, 100)\n", "num"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Next, print an introduction to the game and explain the rules"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"collapsed": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["WELCOME TO GUESS ME!\n", "I'm thinking of a number between 1 and 100\n", "If your guess is more than 10 away from my number, I'll tell you you're COLD\n", "If your guess is within 10 of my number, I'll tell you you're WARM\n", "If your guess is farther than your most recent guess, I'll say you're getting COLDER\n", "If your guess is closer than your most recent guess, I'll say you're getting WARMER\n", "LET'S PLAY!\n"]}], "source": ["print(\"WELCOME TO GUESS ME!\")\n", "print(\"I'm thinking of a number between 1 and 100\")\n", "print(\"If your guess is more than 10 away from my number, I'll tell you you're COLD\")\n", "print(\"If your guess is within 10 of my number, I'll tell you you're WARM\")\n", "print(\"If your guess is farther than your most recent guess, I'll say you're getting COLDER\")\n", "print(\"If your guess is closer than your most recent guess, I'll say you're getting WARMER\")\n", "print(\"LET'S PLAY!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Create a list to store guesses\n", "\n", "Hint: zero is a good placeholder value. It's useful because it evaluates to \"False\""]}, {"cell_type": "code", "execution_count": 10, "metadata": {"collapsed": true}, "outputs": [], "source": ["guesses = [0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Write a `while` loop that asks for a valid guess. Test it a few times to make sure it works."]}, {"cell_type": "code", "execution_count": 11, "metadata": {"collapsed": true}, "outputs": [], "source": ["while True:\n", "    \n", "    guess = int(input(\"I am thinking of a number between 1 and 100. What is your guess? \"))\n", "\n", "    if guess < 1 or guess > 100:\n", "        print(\"OUT OF BOUNDS! Please try again: \")\n", "        continue\n", "    break"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Write a `while` loop that compares the player's guess to our number. If the player guesses correctly, break from the loop. Otherwise, tell the player if they're warmer or colder, and continue asking for guesses.\n", "\n", "Some hints:\n", "* it may help to sketch out all possible combinations on paper first!\n", "* you can use the `abs()` function to find the positive difference between two numbers\n", "* if you append all new guesses to the list, then the previous guess is given as `guesses[-2]`"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"collapsed": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["WARMER!\n", "WARMER!\n", "COLDER!\n", "WARMER!\n", "COLDER!\n", "WARMER!\n", "WARMER!\n", "COLDER!\n", "WARMER!\n", "Congratulations! You guessed the number!\n"]}], "source": ["while True:\n", "    \n", "    guess = int(input(\"I am thinking of a number between 1 and 100. What is your guess? \"))\n", "\n", "    if guess < 1 or guess > 100:\n", "        print(\"OUT OF BOUNDS! Please try again: \")\n", "        continue\n", "    \n", "    if guess == num:\n", "        print(\"Congratulations! You guessed the number!\")\n", "        break\n", "\n", "    guesses.append(guess)\n", "\n", "    if len(guesses) == 1:\n", "            if abs(num - guess) <= 10:\n", "                print(\"WARM!\")\n", "            else:\n", "                print(\"COLD!\")\n", "    else:\n", "             # For subsequent guesses\n", "            if abs(num - guess) < abs(num - guesses[-2]):  # Compare with previous guess\n", "                print(\"WARMER!\")\n", "            elif abs(num - guess) > abs(num - guesses[-2]):\n", "                print(\"COLDER!\")\n", "            else:\n", "                print(\"SAME DISTANCE!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["That's it! You've just programmed your first game!\n", "\n", "In the next section we'll learn how to turn some of these repetitive actions into *functions* that can be called whenever we need them."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Good Job!"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 2}