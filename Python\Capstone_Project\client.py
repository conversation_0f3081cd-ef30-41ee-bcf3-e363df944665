from ftplib import FTP
import os


class FTPClient:
    def __init__(self, host="127.0.0.1", port=21):
        self.ftp = FTP()
        self.host = host
        self.port = port

    def connect(self):
        try:
            self.ftp.connect(self.host, self.port)
            self.ftp.login()
            print(f"Connected to FTP server at {self.host}:{self.port}")
        except Exception as e:
            print(f"Failed to connect: {e}")

    def upload(self):
        filename = input("Enter the path of the file to upload:")

        if not os.path.isfile(filename):
            print("File does not exist.")
            return

        with open(filename, "rb") as file:
            self.ftp.storbinary(f"STOR {os.path.basename(filename)}", file)

        print(f"File {filename} uploaded successfully.")

    def download(self):
        remote_filename = input("Enter the filename to download From the server: ")

        try:
            with open(remote_filename, "wb") as file:
                self.ftp.retrbinary(f"RETR {remote_filename}", file.write)
            print(f"{remote_filename} downloaded successfully.")
        except Exception as e:
            print(f"Error downloading {remote_filename} : {e}")

    def list_files(self):
        print("Files on server:")
        self.ftp.retrlines("LIST")

    def close(self):
        self.ftp.quit()


if __name__ == "__main__":
    client = FTPClient()

    client.connect()

    while True:
        print("\n1. Upload file")
        print("2. Download file")
        print("3. List files on Server")
        print("4. Quit")

        choice = input("Choose an option: ")

        if choice == "1":
            client.upload()
        elif choice == "2":
            client.download()
        elif choice == "3":
            client.list_files()
        elif choice == "4":
            client.close()
            break
        else:
            print("Invalid Choice. Try again.")