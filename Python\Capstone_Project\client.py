from ftplib import FTP
import os
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading


class FTPClient:
    def __init__(self):
        self.ftp = FTP()
        self.host = "127.0.0.1"
        self.port = 21
        self.current_dir = "/"
        self.connected = False

        self.root = tk.Tk()
        self.root.title("FTP Client - Capstone Project")
        self.root.geometry("800x600")
        self.root.configure(bg="#f0f0f0")

        self.setup_ui()

    def setup_ui(self):
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(4, weight=1)

        ttk.Label(main_frame, text="FTP Server Connection", font=("Arial", 14, "bold")).grid(row=0, column=0, columnspan=3, pady=(0, 10))

        ttk.Label(main_frame, text="Host:").grid(row=1, column=0, sticky=tk.W, padx=(0, 5))
        self.host_entry = ttk.Entry(main_frame, width=20)
        self.host_entry.insert(0, "127.0.0.1")
        self.host_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(0, 10))

        self.connect_btn = ttk.Button(main_frame, text="Connect", command=self.connect)
        self.connect_btn.grid(row=1, column=2)

        self.status_label = ttk.Label(main_frame, text="Status: Not connected", foreground="red")
        self.status_label.grid(row=2, column=0, columnspan=3, pady=(5, 10))

        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=3, pady=(0, 10), sticky=(tk.W, tk.E))

        ttk.Button(button_frame, text="Upload File", command=self.upload_file).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="Download File", command=self.download_file).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="Download All", command=self.download_all_files).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="Create Directory", command=self.create_directory).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="Refresh", command=self.refresh_files).pack(side=tk.LEFT)

        files_frame = ttk.LabelFrame(main_frame, text="Server Files", padding="5")
        files_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        files_frame.columnconfigure(0, weight=1)
        files_frame.rowconfigure(1, weight=1)

        self.current_dir_label = ttk.Label(files_frame, text="Current Directory: /")
        self.current_dir_label.grid(row=0, column=0, sticky=tk.W, pady=(0, 5))

        self.file_tree = ttk.Treeview(files_frame, columns=("Type", "Size"), show="tree headings")
        self.file_tree.heading("#0", text="Name")
        self.file_tree.heading("Type", text="Type")
        self.file_tree.heading("Size", text="Size")
        self.file_tree.column("#0", width=300)
        self.file_tree.column("Type", width=100)
        self.file_tree.column("Size", width=100)
        self.file_tree.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.file_tree.bind("<Double-1>", self.on_item_double_click)

        scrollbar = ttk.Scrollbar(files_frame, orient=tk.VERTICAL, command=self.file_tree.yview)
        scrollbar.grid(row=1, column=1, sticky=(tk.N, tk.S))
        self.file_tree.configure(yscrollcommand=scrollbar.set)

        log_frame = ttk.LabelFrame(main_frame, text="Activity Log", padding="5")
        log_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

        self.log_text = scrolledtext.ScrolledText(log_frame, height=8, state=tk.DISABLED)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        main_frame.rowconfigure(5, weight=1)

    def log_message(self, message):
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)

    def connect(self):
        self.host = self.host_entry.get()
        try:
            self.ftp.connect(self.host, self.port)
            self.ftp.login()
            self.current_dir = self.ftp.pwd()
            self.connected = True
            self.status_label.config(text=f"Status: Connected to {self.host}", foreground="green")
            self.current_dir_label.config(text=f"Current Directory: {self.current_dir}")
            self.log_message(f"Connected to FTP server at {self.host}:{self.port}")
            self.refresh_files()
            self.connect_btn.config(text="Disconnect", command=self.disconnect)
        except Exception as e:
            messagebox.showerror("Connection Error", f"Failed to connect: {e}")
            self.log_message(f"Connection failed: {e}")

    def disconnect(self):
        try:
            self.ftp.quit()
            self.connected = False
            self.status_label.config(text="Status: Not connected", foreground="red")
            self.current_dir_label.config(text="Current Directory: /")
            self.file_tree.delete(*self.file_tree.get_children())
            self.log_message("Disconnected from FTP server")
            self.connect_btn.config(text="Connect", command=self.connect)
        except:
            pass

    def upload(self):
        filename = input("Enter the path of the file to upload: ")

        if not os.path.isfile(filename):
            print("File does not exist.")
            return

        try:
            with open(filename, "rb") as file:
                self.ftp.storbinary(f"STOR {os.path.basename(filename)}", file)
            print(f"File {filename} uploaded successfully to {self.current_dir}")
        except Exception as e:
            print(f"Error uploading file: {e}")

    def download(self):
        remote_filename = input("Enter the filename to download from the server: ")

        # Set default download directory to Client_storage
        local_directory = "Client_storage"

        # Use original filename
        local_filename = remote_filename

        # Create the full local path
        local_path = os.path.join(local_directory, local_filename)

        try:
            # Create the Client_storage directory if it doesn't exist
            os.makedirs(local_directory, exist_ok=True)

            with open(local_path, "wb") as file:
                self.ftp.retrbinary(f"RETR {remote_filename}", file.write)
            print(f"{remote_filename} downloaded successfully to {local_path}")
            print(f"Downloaded from server directory: {self.current_dir}")
        except Exception as e:
            print(f"Error downloading {remote_filename}: {e}")

    def list_files(self):
        print(f"Contents of directory: {self.current_dir}")
        try:
            # Store the file listing in a list for display
            file_list = []
            self.ftp.retrlines("LIST", file_list.append)

            # Display files and directories with formatting
            print("\nName                    Type        Size")
            print("-" * 50)
            for item in file_list:
                parts = item.split()
                if len(parts) >= 9:  # Standard Unix format
                    file_type = "Directory" if parts[0].startswith("d") else "File"
                    size = parts[4]
                    name = " ".join(parts[8:])
                    print(f"{name:<25} {file_type:<12} {size}")
        except Exception as e:
            print(f"Error listing files: {e}")

    def get_current_directory(self):
        try:
            self.current_dir = self.ftp.pwd()
            print(f"Current directory: {self.current_dir}")
        except Exception as e:
            print(f"Error getting current directory: {e}")

    def change_directory(self):
        try:
            path = input("Enter directory path (use '..' to go up one level): ")
            self.ftp.cwd(path)
            self.current_dir = self.ftp.pwd()
            print(f"Changed to directory: {self.current_dir}")
            # List files in the new directory
            self.list_files()
        except Exception as e:
            print(f"Error changing directory: {e}")

    def create_directory(self):
        try:
            dir_name = input("Enter name for new directory: ")
            self.ftp.mkd(dir_name)
            print(f"Directory '{dir_name}' created successfully.")
        except Exception as e:
            print(f"Error creating directory: {e}")

    def download_all_files(self):
        """Download all files from current directory to Client_storage folder"""
        local_directory = "Client_storage"

        try:
            # Create the Client_storage directory if it doesn't exist
            os.makedirs(local_directory, exist_ok=True)

            # Get list of files in current directory
            file_list = []
            self.ftp.retrlines("LIST", file_list.append)

            downloaded_count = 0
            print(f"Downloading all files from {self.current_dir} to {local_directory}...")

            for item in file_list:
                parts = item.split()
                if len(parts) >= 9 and not parts[0].startswith("d"):  # Not a directory
                    filename = " ".join(parts[8:])
                    local_path = os.path.join(local_directory, filename)

                    try:
                        with open(local_path, "wb") as file:
                            self.ftp.retrbinary(f"RETR {filename}", file.write)
                        print(f"Downloaded: {filename}")
                        downloaded_count += 1
                    except Exception as e:
                        print(f"Error downloading {filename}: {e}")

            print(f"\nDownload complete! {downloaded_count} files downloaded to {local_directory}")

        except Exception as e:
            print(f"Error downloading files: {e}")

    def close(self):
        self.ftp.quit()


if __name__ == "__main__":
    client = FTPClient()

    client.connect()

    while True:
        print("\n===== FTP Client Menu =====")
        print("1. Upload file")
        print("2. Download file")
        print("3. Download all files from current directory")
        print("4. List files in current directory")
        print("5. Change directory")
        print("6. Show current directory")
        print("7. Create new directory")
        print("8. Quit")
        print("==========================")

        choice = input("Choose an option: ")

        if choice == "1":
            client.upload()
        elif choice == "2":
            client.download()
        elif choice == "3":
            client.download_all_files()
        elif choice == "4":
            client.list_files()
        elif choice == "5":
            client.change_directory()
        elif choice == "6":
            client.get_current_directory()
        elif choice == "7":
            client.create_directory()
        elif choice == "8":
            client.close()
            print("Disconnected from FTP server. Goodbye!")
            break
        else:
            print("Invalid Choice. Try again.")