from ftplib import FTP
import os


class FTPClient:
    def __init__(self, host="127.0.0.1", port=21):
        self.ftp = FTP()
        self.host = host
        self.port = port
        self.current_dir = "/"

    def connect(self):
        try:
            self.ftp.connect(self.host, self.port)
            self.ftp.login()
            self.current_dir = self.ftp.pwd()
            print(f"Connected to FTP server at {self.host}:{self.port}")
            print(f"Current directory: {self.current_dir}")
        except Exception as e:
            print(f"Failed to connect: {e}")

    def upload(self):
        filename = input("Enter the path of the file to upload: ")

        if not os.path.isfile(filename):
            print("File does not exist.")
            return

        try:
            with open(filename, "rb") as file:
                self.ftp.storbinary(f"STOR {os.path.basename(filename)}", file)
            print(f"File {filename} uploaded successfully to {self.current_dir}")
        except Exception as e:
            print(f"Error uploading file: {e}")

    def download(self):
        remote_filename = input("Enter the filename to download from the server: ")
        local_filename = input("Enter the local filename to save as (press Enter to use the same name): ")

        if not local_filename:
            local_filename = remote_filename

        try:
            with open(local_filename, "wb") as file:
                self.ftp.retrbinary(f"RETR {remote_filename}", file.write)
            print(f"{remote_filename} downloaded successfully as {local_filename}")
            print(f"Downloaded from directory: {self.current_dir}")
        except Exception as e:
            print(f"Error downloading {remote_filename}: {e}")

    def list_files(self):
        print(f"Contents of directory: {self.current_dir}")
        try:
            # Store the file listing in a list for display
            file_list = []
            self.ftp.retrlines("LIST", file_list.append)

            # Display files and directories with formatting
            print("\nName                    Type        Size")
            print("-" * 50)
            for item in file_list:
                parts = item.split()
                if len(parts) >= 9:  # Standard Unix format
                    file_type = "Directory" if parts[0].startswith("d") else "File"
                    size = parts[4]
                    name = " ".join(parts[8:])
                    print(f"{name:<25} {file_type:<12} {size}")
        except Exception as e:
            print(f"Error listing files: {e}")

    def get_current_directory(self):
        try:
            self.current_dir = self.ftp.pwd()
            print(f"Current directory: {self.current_dir}")
        except Exception as e:
            print(f"Error getting current directory: {e}")

    def change_directory(self):
        try:
            path = input("Enter directory path (use '..' to go up one level): ")
            self.ftp.cwd(path)
            self.current_dir = self.ftp.pwd()
            print(f"Changed to directory: {self.current_dir}")
            # List files in the new directory
            self.list_files()
        except Exception as e:
            print(f"Error changing directory: {e}")

    def create_directory(self):
        try:
            dir_name = input("Enter name for new directory: ")
            self.ftp.mkd(dir_name)
            print(f"Directory '{dir_name}' created successfully.")
        except Exception as e:
            print(f"Error creating directory: {e}")

    def close(self):
        self.ftp.quit()


if __name__ == "__main__":
    client = FTPClient()

    client.connect()

    while True:
        print("\n===== FTP Client Menu =====")
        print("1. Upload file")
        print("2. Download file")
        print("3. List files in current directory")
        print("4. Change directory")
        print("5. Show current directory")
        print("6. Create new directory")
        print("7. Quit")
        print("==========================")

        choice = input("Choose an option: ")

        if choice == "1":
            client.upload()
        elif choice == "2":
            client.download()
        elif choice == "3":
            client.list_files()
        elif choice == "4":
            client.change_directory()
        elif choice == "5":
            client.get_current_directory()
        elif choice == "6":
            client.create_directory()
        elif choice == "7":
            client.close()
            print("Disconnected from FTP server. Goodbye!")
            break
        else:
            print("Invalid Choice. Try again.")