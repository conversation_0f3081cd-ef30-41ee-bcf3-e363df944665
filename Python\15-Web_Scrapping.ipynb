{"cells": [{"cell_type": "markdown", "id": "cb219f61", "metadata": {}, "source": ["# Module 15: Web Scrapping"]}, {"cell_type": "markdown", "id": "90c83027", "metadata": {}, "source": ["**Introduction to Web Scraping**\n", "\n", "Web scraping is the process of automatically extracting data from websites. It involves retrieving HTML content and parsing it to collect the information you need.\n", "\n", "- **Libraries**: Popular Python libraries include `requests` for downloading web pages and `BeautifulSoup` or `lxml` for parsing HTML.\n", "- **Usage**: Commonly used for data collection, price monitoring, research, and content aggregation.\n", "- **Considerations**: Always respect a website’s `robots.txt`, terms of service, and avoid overloading servers with too many requests.\n", "\n", "Web scraping helps automate data gathering but should be done responsibly and ethically.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "5e0b7d9c", "metadata": {}, "outputs": [], "source": ["# %pip install requests -q\n", "# %pip install bs4 -q\n", "# %pip install lxml -q"]}, {"cell_type": "code", "execution_count": 2, "id": "9f0c870d", "metadata": {}, "outputs": [], "source": ["import requests\n", "import bs4\n", "import lxml"]}, {"cell_type": "code", "execution_count": 3, "id": "2d89d01e", "metadata": {}, "outputs": [], "source": ["result = requests.get(\"http://www.example.com\")"]}, {"cell_type": "code", "execution_count": 4, "id": "338c181f", "metadata": {}, "outputs": [{"data": {"text/plain": ["requests.models.Response"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["type(result)"]}, {"cell_type": "code", "execution_count": 5, "id": "0c9a2140", "metadata": {}, "outputs": [{"data": {"text/plain": ["'<!doctype html>\\n<html>\\n<head>\\n    <title>Example Domain</title>\\n\\n    <meta charset=\"utf-8\" />\\n    <meta http-equiv=\"Content-type\" content=\"text/html; charset=utf-8\" />\\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\" />\\n    <style type=\"text/css\">\\n    body {\\n        background-color: #f0f0f2;\\n        margin: 0;\\n        padding: 0;\\n        font-family: -apple-system, system-ui, BlinkMacSystemFont, \"Segoe UI\", \"Open Sans\", \"Helvetica Neue\", Helvetica, Arial, sans-serif;\\n        \\n    }\\n    div {\\n        width: 600px;\\n        margin: 5em auto;\\n        padding: 2em;\\n        background-color: #fdfdff;\\n        border-radius: 0.5em;\\n        box-shadow: 2px 3px 7px 2px rgba(0,0,0,0.02);\\n    }\\n    a:link, a:visited {\\n        color: #38488f;\\n        text-decoration: none;\\n    }\\n    @media (max-width: 700px) {\\n        div {\\n            margin: 0 auto;\\n            width: auto;\\n        }\\n    }\\n    </style>    \\n</head>\\n\\n<body>\\n<div>\\n    <h1>Example Domain</h1>\\n    <p>This domain is for use in illustrative examples in documents. You may use this\\n    domain in literature without prior coordination or asking for permission.</p>\\n    <p><a href=\"https://www.iana.org/domains/example\">More information...</a></p>\\n</div>\\n</body>\\n</html>\\n'"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["result.text"]}, {"cell_type": "code", "execution_count": 6, "id": "685a8f7b", "metadata": {}, "outputs": [], "source": ["soup = bs4.BeautifulSoup(result.text,'lxml')"]}, {"cell_type": "code", "execution_count": 7, "id": "4fdc48e4", "metadata": {}, "outputs": [{"data": {"text/plain": ["<!DOCTYPE html>\n", "<html>\n", "<head>\n", "<title>Example Domain</title>\n", "<meta charset=\"utf-8\"/>\n", "<meta content=\"text/html; charset=utf-8\" http-equiv=\"Content-type\"/>\n", "<meta content=\"width=device-width, initial-scale=1\" name=\"viewport\"/>\n", "<style type=\"text/css\">\n", "    body {\n", "        background-color: #f0f0f2;\n", "        margin: 0;\n", "        padding: 0;\n", "        font-family: -apple-system, system-ui, BlinkMacSystemFont, \"Segoe UI\", \"Open Sans\", \"Helvetica Neue\", Helvetica, Arial, sans-serif;\n", "        \n", "    }\n", "    div {\n", "        width: 600px;\n", "        margin: 5em auto;\n", "        padding: 2em;\n", "        background-color: #fdfdff;\n", "        border-radius: 0.5em;\n", "        box-shadow: 2px 3px 7px 2px rgba(0,0,0,0.02);\n", "    }\n", "    a:link, a:visited {\n", "        color: #38488f;\n", "        text-decoration: none;\n", "    }\n", "    @media (max-width: 700px) {\n", "        div {\n", "            margin: 0 auto;\n", "            width: auto;\n", "        }\n", "    }\n", "    </style>\n", "</head>\n", "<body>\n", "<div>\n", "<h1>Example Domain</h1>\n", "<p>This domain is for use in illustrative examples in documents. You may use this\n", "    domain in literature without prior coordination or asking for permission.</p>\n", "<p><a href=\"https://www.iana.org/domains/example\">More information...</a></p>\n", "</div>\n", "</body>\n", "</html>"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["soup"]}, {"cell_type": "code", "execution_count": 8, "id": "8eae46a2", "metadata": {}, "outputs": [{"data": {"text/plain": ["[<title>Example Domain</title>]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["soup.select('title')"]}, {"cell_type": "code", "execution_count": 9, "id": "2725fd38", "metadata": {}, "outputs": [{"data": {"text/plain": ["[<p>This domain is for use in illustrative examples in documents. You may use this\n", "     domain in literature without prior coordination or asking for permission.</p>,\n", " <p><a href=\"https://www.iana.org/domains/example\">More information...</a></p>]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["soup.select('p')"]}, {"cell_type": "code", "execution_count": 10, "id": "126f9fcd", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Example Domain'"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["soup.select('title')[0].getText()"]}, {"cell_type": "code", "execution_count": 11, "id": "351c97cf", "metadata": {}, "outputs": [], "source": ["site_paragraphs = soup.select('p')"]}, {"cell_type": "code", "execution_count": 12, "id": "baa3fa36", "metadata": {}, "outputs": [{"data": {"text/plain": ["bs4.element.Tag"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["type(site_paragraphs[0])"]}, {"cell_type": "code", "execution_count": 13, "id": "2eede43f", "metadata": {}, "outputs": [{"data": {"text/plain": ["'This domain is for use in illustrative examples in documents. You may use this\\n    domain in literature without prior coordination or asking for permission.'"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["site_paragraphs[0].getText()"]}, {"cell_type": "markdown", "id": "a049703e", "metadata": {}, "source": ["### Extract Data using soup.select()"]}, {"cell_type": "markdown", "id": "642ab5a8", "metadata": {}, "source": ["**`soup.select()` Syntax and Usage**\n", "\n", "`soup.select()` is a BeautifulSoup method used to find elements using CSS selectors.\n", "\n", "- **Syntax**:\n", "  ```python\n", "  soup.select(selector)\n", "  ```\n", "\n", "- **Parameters**:\n", "  - `selector`: A string containing a CSS selector.\n", "\n", "- **Usage**:\n", "  - Returns a list of matching elements.\n", "  - Supports tag names, classes (`.class`), IDs (`#id`), attributes, and nested selectors.\n", "\n", "**Examples of Selectors**\n", "\n", "| Selector       | What It Matches                                               |\n", "|----------------|----------------------------------------------------------------|\n", "| `div`          | All `<div>` tags                                              |\n", "| `.class-name`  | Elements with a specific class                                |\n", "| `#id-name`     | The element with a specific ID                                |\n", "| `div > p`      | `<p>` tags that are direct children of `<div>`                |\n", "| `a[href]`      | All `<a>` tags with an `href` attribute                       |\n", "\n", "`soup.select()` is commonly used for quickly locating elements in parsed HTML.\n"]}, {"cell_type": "code", "execution_count": 14, "id": "c6b25c5d", "metadata": {}, "outputs": [], "source": ["res = requests.get(\"https://en.wikipedia.org/wiki/<PERSON>_<PERSON>\")"]}, {"cell_type": "code", "execution_count": 15, "id": "705a7ce7", "metadata": {}, "outputs": [], "source": ["soup = bs4.BeautifulSoup(res.text,'lxml')"]}, {"cell_type": "code", "execution_count": 16, "id": "b7532279", "metadata": {}, "outputs": [], "source": ["first_item = soup.select('.vector-toc-text')[1].select('span')[1].text"]}, {"cell_type": "code", "execution_count": 17, "id": "f3e77100", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Early life and education'"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["first_item"]}, {"cell_type": "code", "execution_count": 18, "id": "a67f2489", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Early life and education\n", "Career\n", "World War II\n", "UNIVAC\n", "COBOL\n", "Standards\n", "Retirement\n", "Post-retirement\n", "Anecdotes\n", "Death\n", "Dates of rank\n", "Awards and honors\n", "Military awards\n", "Other awards\n", "Legacy\n", "Places\n", "Programs\n", "In popular culture\n", "<PERSON> of Women in Computing\n", "See also\n", "Notes\n", "References\n", "Obituary notices\n", "Further reading\n", "External links\n"]}], "source": ["span_elements = soup.select('.vector-toc-text span')\n", "\n", "for i in range(1,len(span_elements),2):\n", "    print(span_elements[i].text)\n"]}, {"cell_type": "markdown", "id": "f60488f3", "metadata": {}, "source": ["### Extract Image from Web page."]}, {"cell_type": "code", "execution_count": 19, "id": "0ba8ed8d", "metadata": {}, "outputs": [], "source": ["res = requests.get('https://en.wikipedia.org/wiki/Deep_Blue_(chess_computer)')"]}, {"cell_type": "code", "execution_count": 20, "id": "a533a12b", "metadata": {}, "outputs": [], "source": ["soup = bs4.BeautifulSoup(res.text,'lxml')"]}, {"cell_type": "code", "execution_count": 21, "id": "b024a889", "metadata": {}, "outputs": [], "source": ["computer = soup.select('.mw-file-element')[1]"]}, {"cell_type": "code", "execution_count": 22, "id": "0737da80", "metadata": {}, "outputs": [{"data": {"text/plain": ["'//upload.wikimedia.org/wikipedia/commons/thumb/b/be/Deep_Blue.jpg/250px-Deep_Blue.jpg'"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["computer['src']"]}, {"cell_type": "markdown", "id": "4b800e54", "metadata": {}, "source": ["<img src = '//upload.wikimedia.org/wikipedia/commons/thumb/b/be/Deep_Blue.jpg/250px-Deep_Blue.jpg'>"]}, {"cell_type": "code", "execution_count": 23, "id": "8e7bce87", "metadata": {}, "outputs": [], "source": ["image_link = requests.get('https://upload.wikimedia.org/wikipedia/commons/thumb/b/be/Deep_Blue.jpg/250px-Deep_Blue.jpg')"]}, {"cell_type": "code", "execution_count": 24, "id": "adac329d", "metadata": {}, "outputs": [{"data": {"text/plain": ["b'\\xff\\xd8\\xff\\xe0\\x00\\x10JFIF\\x00\\x01\\x01\\x01\\x00H\\x00H\\x00\\x00\\xff\\xfe\\x00CFile source: http://commons.wikimedia.org/wiki/File:Deep_Blue.jpg\\xff\\xe2\\x02@ICC_PROFILE\\x00\\x01\\x01\\x00\\x00\\x020ADBE\\x02\\x10\\x00\\x00mntrRGB XYZ \\x07\\xcf\\x00\\x06\\x00\\x03\\x00\\x00\\x00\\x00\\x00\\x00acspAPPL\\x00\\x00\\x00\\x00none\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\xf6\\xd6\\x00\\x01\\x00\\x00\\x00\\x00\\xd3-ADBE\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\ncprt\\x00\\x00\\x00\\xfc\\x00\\x00\\x002desc\\x00\\x00\\x010\\x00\\x00\\x00kwtpt\\x00\\x00\\x01\\x9c\\x00\\x00\\x00\\x14bkpt\\x00\\x00\\x01\\xb0\\x00\\x00\\x00\\x14rTRC\\x00\\x00\\x01\\xc4\\x00\\x00\\x00\\x0egTRC\\x00\\x00\\x01\\xd4\\x00\\x00\\x00\\x0ebTRC\\x00\\x00\\x01\\xe4\\x00\\x00\\x00\\x0erXYZ\\x00\\x00\\x01\\xf4\\x00\\x00\\x00\\x14gXYZ\\x00\\x00\\x02\\x08\\x00\\x00\\x00\\x14bXYZ\\x00\\x00\\x02\\x1c\\x00\\x00\\x00\\x14text\\x00\\x00\\x00\\x00Copyright 1999 Adobe Systems Incorporated\\x00\\x00\\x00desc\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x11Adobe RGB (1998)\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00XYZ \\x00\\x00\\x00\\x00\\x00\\x00\\xf3Q\\x00\\x01\\x00\\x00\\x00\\x01\\x16\\xccXYZ \\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00curv\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x01\\x023\\x00\\x00curv\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x01\\x023\\x00\\x00curv\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x01\\x023\\x00\\x00XYZ \\x00\\x00\\x00\\x00\\x00\\x00\\x9c\\x18\\x00\\x00O\\xa5\\x00\\x00\\x04\\xfcXYZ \\x00\\x00\\x00\\x00\\x00\\x004\\x8d\\x00\\x00\\xa0,\\x00\\x00\\x0f\\x95XYZ \\x00\\x00\\x00\\x00\\x00\\x00&1\\x00\\x00\\x10/\\x00\\x00\\xbe\\x9c\\xff\\xdb\\x00C\\x00\\x06\\x04\\x05\\x06\\x05\\x04\\x06\\x06\\x05\\x06\\x07\\x07\\x06\\x08\\n\\x10\\n\\n\\t\\t\\n\\x14\\x0e\\x0f\\x0c\\x10\\x17\\x14\\x18\\x18\\x17\\x14\\x16\\x16\\x1a\\x1d%\\x1f\\x1a\\x1b#\\x1c\\x16\\x16 , #&\\')*)\\x19\\x1f-0-(0%()(\\xff\\xdb\\x00C\\x01\\x07\\x07\\x07\\n\\x08\\n\\x13\\n\\n\\x13(\\x1a\\x16\\x1a((((((((((((((((((((((((((((((((((((((((((((((((((\\xff\\xc0\\x00\\x11\\x08\\x01x\\x00\\xfa\\x03\\x01\\x11\\x00\\x02\\x11\\x01\\x03\\x11\\x01\\xff\\xc4\\x00\\x1d\\x00\\x00\\x00\\x07\\x01\\x01\\x01\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x01\\x02\\x03\\x04\\x05\\x07\\x08\\x06\\t\\xff\\xc4\\x00X\\x10\\x00\\x01\\x03\\x02\\x03\\x05\\x04\\x05\\x06\\x08\\t\\x08\\x08\\x07\\x00\\x00\\x01\\x02\\x03\\x11\\x00\\x04\\x05!1\\x06\\x12AQa\\x07\\x13\"q2\\x81\\x91\\xa1\\xb1\\x08\\x14#B\\xc1\\xd1\\x15$34Rbr\\xe1%Scs\\x92\\xa2\\xa3\\xb2\\xf0\\x16C\\x82\\x83\\x84\\xb3\\xc2\\xf1&\\'Ede\\x93\\x94\\xd2\\x1756FTV\\xc3\\xff\\xc4\\x00\\x1b\\x01\\x01\\x01\\x01\\x01\\x01\\x01\\x01\\x01\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x01\\x00\\x02\\x03\\x04\\x05\\x06\\x07\\xff\\xc4\\x007\\x11\\x01\\x01\\x00\\x02\\x01\\x03\\x02\\x03\\x05\\x07\\x03\\x04\\x03\\x01\\x00\\x00\\x00\\x01\\x02\\x11!\\x03\\x121AQ\\x04\\x13a\\x052q\\xc1\\xf0\"3\\x81\\x91\\xa1\\xb1\\xd1\\x14#\\xe1\\x06b\\x92\\xa2\\x15B\\xf1r\\xff\\xda\\x00\\x0c\\x03\\x01\\x00\\x02\\x11\\x03\\x11\\x00?\\x00\\xf5\\xb7\\xdbw\\x87\\xfe\\x11\\xc2./Xv\\xdd\\x0c]\\xb6\\xb5\\xa9?H\\x00\\x9dG\\x1a\\xf4e\\xf6_Re;l\\xbf\\xd1\\xcf\\x0f\\xb4zz\\xbd\\xd2\\xc6\\xc7\\x85b\\xb6\\x18\\xbb\\n\\xb8\\xc2\\xee\\xda\\xbad+t\\xa9\\xb3\\xa1\\xe4y\\x1a\\xf2uzY\\xf4ooRj\\xbd\\x1d>\\xae=Lw\\x85\\xdamsl\\xaa\\x90\\n\\x80\\xe9Fn\\xedX\\xbd\\xb6r\\xda\\xed\\x96\\xdf\\xb7pB\\xdbq!IW\\x98\\xa9M\\xcb\\xb8\\xa6-b\\x98\"\\x9cr\\xddO\\xe2\\xf8h\\x04\\x8bU\\xa8\\x1b\\xa6\\x7f\\x9bY#\\xbd\\x1f\\xaa\\xb3\\xbd\\xfa\\xc7!I\\xe3/\\xa5\\xfe\\x9f\\xf1\\xfd\\x96\\xf8~!k\\x884\\xa5\\xd9\\xbc\\x97B\\x0e\\xea\\xd3\\x05*m_\\xa2\\xb4\\x98)=\\x08\\x06\\xaf\\xc1\\x9b,\\xe2\\xa5T\\x07Py\\xad\\xb8p7ij`\\x92\\r\\xca\\xb2\\xe4-\\x1e\\xa5\\xbc|\\xfe\\xbd\\xceYa\\x97\\xd6)\\xef0\\x8b\\xe1\\xdc\\xa8\\x02l\\xee\\xc1[IQH2\\xda\\x87\\x89\\x03\\xf5|I\\xe4\\x13TW[\\xe4\\xee\\x1b\\x84+\\x0b\\xc3\\xb1\\x12\\xfd\\xd2\\xae\\xeen{\\xc7\\x1cp\\xa0 g\\xbcwBF\\x80\\x15\\x1e\\xb5N\\x05\\xbb\\xd2u\\xb0\\xfa4~\\xc8\\xf8V\\x98<\\xe0\\x86\\xc7\\xed\\n3\\xfb\\xadc\\xe5\\xcf\\x7f(\\xc1\\xff\\x00M0\\xa3\\xff\\x00\\x85\\x8c\\xff\\x00\\xd6\\xaa\\x89\\xe2\\x1fw\\x86\\xd8\\xd4\\x95\\xed\\x86\\x02\\x94\\x85\\x95~\\x10\\xb7\\x80\\xd9\\x01_\\x94\\x1a\\x13\\xc7*\\xe9\\xd2\\x9b\\xcb\\xf5\\xec\\xc6w\\xf6]\\x85\\xf4\\xe9u).\\x82\\x14TG\\x87@4\\xa7\\\\m\\x80\\x0f/\\xbd\\xee\\xd3\\xb8\\xb2=\"\\x01\\x01>uk\\x8d\\xad\\x9cC\\xa5{\\xd0\\x8fD\\xee\\x9c\\xf8\\xd5f\\x94\\xa8\\xf6\\x98\\xad\\x85\\xe2\\xd6\\x8bK\\xcbg\\xd6\\xda\\x8aV\\x96\\x9dJ\\xcaH\\xd4\\x10\\x0eF\\xab,\\x13,o\\x8a\\x97\\xbe\\x9e&<\\xe8ha@\\xe8A\\xa9\\x0e\\xa4\\x15 \\xa9\\x05H*AR\\n\\x90T\\x9cO\\x8b\\xe2\\t\\xb8[(P)h:\\x0b\\x9b\\xa7=\\xde\\x9dt\\xaf\\xd2\\xf5p\\xcb\\x1c\\xa7o\\x9d\\xc7\\xc3\\xe9e\\x8e[\\xef\\xf1\\xaa\\xdd~O\\xcb?\\x80\\xb1d\\x13;\\xb7I>\\xd4W\\x83\\xfe\\xa0\\x9f\\xefa~\\x9f\\x9b\\xdb\\xf67\\x1d\\x1b>\\xad\\\\W\\xc2}a\\xd4\\x86*C\\xa8\\x05(u\\x05~#\\x83\\xda\\xdf\\\\7t{\\xc6/\\x9a\\xc9\\xbb\\xb6\\x15\\xb8\\xeaG\"tR\\x7fU@\\xa7\\xa5\\'vq\\xe8\\x8a\\x9b\\xfb\\xfc1\\xa5\\x9ci\\x94\\xbfn\\x89?<\\xb2mF\\x13\\xcdmf\\xa4\\x9e\\xa9\\xde\\x1cr\\xabKR\\xfd\\xd5\\xb5\\xad\\xc37v\\xcd\\xdcZ\\xbc\\xdb\\xec87\\x90\\xe3J\\nJ\\xc70FF\\x96^wor\\xc3\\xdaW$]g\\xfe\\xca\\xed\\ra\\xeb\\xfa\\xf5z\\x0b\\x1c\\xad\\x91?\\xa2\\x9f\\xee\\x8a\\xa0\\xcb\\xc9W\\x9f\\x9a?\\xfc\\xda\\xbe\\x06\\x98\\xc9\\xab|\\xdbn\\x7fD|+[\\x07\\x1d\\xfc\\x98\\xfd\\xa1\\xf1\\xa3/\\xbbZ\\xc7\\xcb\\x9e\\xbeQ\\xbf\\xfdm\\x84D\\xff\\x00\\xf2\\xcf\\xff\\x00\\xa9\\xac\\xfaE\\xaf/\\x1b\\xb0\\x8b\\r\\xed\\xae\\xcf,\\x85\\x90\\x9cA\\x89\\x01%G\\xd2\\x8e\\x1eu\\xd7\\xa3\\xf7\\xbf\\x9f\\xf6c?\\x0e\\xb6\\xb9K\\xee\\xde[\\x00\\xb4\\xb4\\xc0++\\x11+X\\x8c\\x80?Tq\\xe7\\xa6\\x95\\xb9\\xa9+\\x9d\\x96\\xd8\\x81\\xb4{I\\x85\\xec\\xdb\\x08M\\xd2\\xd4\\xab\\x87\\x01\\xee,\\xad\\x93\\xbe\\xfb\\xc7\\x92\\x10>&\\x00\\xe2EX\\xe1\\x96wc>\\xa4\\xc3\\x8f_eM\\xfe\\x13\\x8ac\\xd7\\xb7\\xf6\\x98\\x8d\\xe2-p\\'\\xdb\\x84\\xdbZ\\x15%\\xe5+y\\x05]\\xe3\\x9d@)\\xddL\\x08Q\\x99\\xae\\x92\\xcc5g\\x97;\\x8d\\xcfs/\\x1e\\xcfA\\x85a8~\\x0bf\\x9b|6\\xd5\\x8bV\\x10 \\x04$\\'.\\xa6\\xb9\\xdc\\xae^k\\xa4\\x92x8\\xbb\\xcd\\xe3\\xb9l\\x9d\\xf2~\\xb1\\x19z\\xb9\\xd1\\xafu\\xbfa\\xb6\\xd3\\x89QS\\xcf/x\\xf0\\x07\\xfc\\x01Q\\x91)\\x1b\\xc7L\\x87Z\\x1a\\x9b84\\xce\\x82\\x15 \\xa9\\x05H*@H\\x02I\\x81E\\xbaDw\\x83\\x92\\xbf\\xa2h\\xee:p}\\xfa\\xc8Z\\x8c\\xfdo}~\\xab\\xadu\\x97\\xf1~w\\xa5\\xf7]\\r\\xf2ywz\\xc7\\x1cO\\'\\x9aW\\xb5&\\xbc\\x1f\\xf5\\x0c\\xd7S\\xa7~\\x95\\xf4>\\xc4\\xfd\\xde\\x7f\\x8f\\xe4\\xd8\\x05~}\\xf6J\\xa8\\x0e\\xa4\\x02\\xa0:P\\xea\\x05n\\xab\\x91\\xa7Tn\\x08\\x82\\x91\\'/]K\\xca\\x82\\xff\\x00\\x0fj\\xdd\\xe7o0\\x8b\\xe6p\\xeb\\xd5\\x92\\xa5\\xa5K\\x06\\xdd\\xf5q\\xef\\x1b\\x9dO\\xe9\\xa6\\x15\\xcc\\x91\\x95=\\xd3\\xdc\\xef\\xddI\\xb4\\xd8\\xb3\\x17\\xd8e\\xa3W\\xaf\\xd9Y]8\\xe3\\xec\\x14|\\xf1\\xb5\\xa2Ul\\xeaRR\\xa9\\x12\\x92`\\r\\xe0\\x93\\xcc\\n7\\x0c\\xe2\\xfe\\xbd\\xd7\\x16\\x1b_\\xb3\\xab\\xb0\\xb7u\\xdc{\\x08k\\xbcm\\x0b\\xdd]\\xe3i\"R5\\x04\\xd5\\xc36\\x91y\\xb6\\xfb&\\x9bw\\x92\\xad\\xa8\\xc0\\x81(P\\x8f\\x9f\\xb5:\\x1f\\xd6\\xabr\\x04;\\x1d\\xbf\\xd8\\xf7\\x0b\\x0c\\xa3jp>\\xf5H\\x10\\x95^!$\\xe49\\x9aw\\x00c\\x1d\\xa2\\xecv\\x1a\\xefq{\\xb4\\xd8Sn\\xef\\x02R\\x1f\\x0b#\\xfa3F\\\\\\xcd\\x19uXom{U\\x80m&\\xd3a\\xd7x.1gun\\xcd\\x8fr\\xe2\\xd2\\xa27W\\xde\\x13\\x10G#T\\xfc\\x0e\\xe7;ym\\x96\\xc6\\xf0\\xec3j0\\x8b\\xeb\\x8b\\xd6\\x8d\\xbd\\xad\\xe3O9\\xb8\\x14\\xa5n\\x05g\\x00\\x0c\\xe0g\\x1d+\\xa7O\\xf6n\\xff\\x00\\x1f\\xeb\\x19\\xcfV6\\x1d\\xa8\\xed\\xc7\\x07~\\xed6\\x98\\x16!\\xf3;oEx\\x9b\\x96\\xabqBu\\xee\\x9a\\x8c\\xff\\x00i\\x7f\\xd15\\xd7\\x1e\\xd9\\xe7\\x97\\x0c\\xaeYx\\xe3\\xfb\\x93\\xb3\\xdd\\xa6\\xf6g\\x818\\xbb\\x86\\xef\\xf1\\x1b\\xdcE\\xdc\\xde\\xbd~\\xc5\\xf7\\x1dp\\xf5QO\\xb8d8S\\x9ew/\\xa38c0\\xf1\\x8a\\xc1]\\xbb\\xec[N)is\\x15rJ\\xa0\\x0b\\x15\\rL\\xf1\"\\x8a\\xd4\\xbfDg{v\\xd9\\x17\\x97/\\'\\x1cX\\x19\\x84\\x8b \\x07\\xf7\\xfe4s\\xe8~\\xba^a=\\xb3\\xec=\\xdb-\\xff\\x00\\x08\\xbd\\x87\\x95\\xabt\\x8b\\xcbU\\xa3w\\xf6\\x96\\x01H\\x1df2\\xac\\xf9]\\xda\\xf4i\\xad!\\x1b\\xa1I!@\\xe6\\x0e\\xb3Sp\\xe5\\x04*AR\\n\\x90M\\x1bB2zQ\\xcd@\\x12\\x01\\x9dO3N\\x90\\xe2\\x94\\xe0,M\\xf0\\x16\\xb8:\\x13_\\xa9\\xf8\\x8e+\\xf3\\xbd\\x0f\\x1bt\\x1f\\xc9\\xc1\\xd0\\xa4\\xe3\\xc8\\xe3\\xf4\\n\\xf7(W\\x8f\\xfe\\xa2\\x9f\\xba\\xbf\\x8b\\xdd\\xf6\\x1f\\xdd\\xeaO\\xac\\xfc\\xdbh\\xaf\\xcd>\\xe0\\xe9\\x03\\x15!\\x8a\\x80\\xe9N}\\xf9D\\xed\\xa6\\xd1`\\xbbUi\\x84`\\xf8\\xb3\\xf6\\x16Na\\xc8\\xb8Rm\\xa1+R\\xd4\\xe3\\x89>8\\x90!\"\"*\\xd6\\xc4a\\xd7W\\xd7\\x8f\\xa9Kv\\xfa\\xf5\\xd9\\x99+\\xb9qS\\xedU3\\x19=\\x15\\xb5\\x11iR\\xcf\\x8dKY\\xd35\\x93\\xf6\\xd3\\xdb=\\x86\\xcc\\xae\\xd9\\xa3\\xe94\\x82:\\xa6b\\x9d-\\x92-YI;\\xad6\\n\\xb20\\x81N\\x99$\\xb2\\xd6\\xa5\\xb6\\xc9\\xe7\\xba)\\xd7+ewh\\x00\\xf8@\\xf2\\x03J\\xad\\xd1\\xda\\xbf\\x18J{\\x84\\x05$\\x11\\xe2\\x91\\x03\\x95[\\xe4,\\x10\\x94\\x8bv\\x92\\x90\\x13\\x1a@\\xcb\\x85\\x18\\xf0i$k\\xac\\xd6\\xe4f\\x8d04\\x15\\x01\\xab\\xcb\\xdbJ\\x1f\\x1f\\xbb\\x85\\x00`\\x99\\xeai\\xfc\\x01h<\\xbdyU\\r>T~h\\xf0\\xe3\\xbat\\xf25O\\xbd\\x05\\xe2;\\xfe\\xd8E\\xbbc\\xf5G\\xc2\\x8c\\xbc\\xd3<\\x1c\\xa0\\x85\\x1bAJ\\x023\\xa2\\xcd\\xa1\\x00w\\xb5\\xcb\\x95R ;\\xdb\\xd9F\\xef\\xbe\\xafT%\\x15\\x05$\\x04\\xc8\\xe2gJ\\xaa\\x1dI\\xc1\\x1bk\\xb3\\xd8\\xd6\\xcf<\\x13\\x8d\\xe1\\xcf\\xda\\x07T\\xb2\\xca\\xd4\\x01C\\xb1\\xae\\xea\\x84\\x83\\x13\\xef\\xaf\\xd2e\\xf1=.\\xbe\\xf2\\xe9\\xe5\\xb7\\xc4\\x9f\\r\\xd4\\xe8k\\x1e\\xa4j]\\x84:\\xe8\\xc6qT[\\xe3\\xeeX8\\xbbf\\x96\\x19\\x01\\x95\\x073\\xfd\\x07\\x04\\x92\\'\\x81\\x1a\\xd7\\x1f\\xb7\\xf9\\xc7\\x0b/\\xfcq\\xf9\\xbb\\xfd\\x8b\\xad\\xe7,\\xfdr\\xdbX\\xb9\\xc7\\x1a\\xdeiX\\xee\\n\\xfb\\xfe\\x90\\x0b\\xb2\\xdd!=B]\\xf2\\xce\\xbf5\\xcd\\xf5}\\xed`{\\xe7\\x9b@\\xda\\xa1\\xcb\\xcc\\x01FG\\x84\\xb2\\xebg\\xfb\\xe6\\xaeV\\xb1\\xfa\\x9fN%\\x8f\\x04 \\x8c+\\x0e\\xb8\\x9dT\\xdd\\xeb\\x88\\x1e\\xa9l\\xcf\\xb6\\xaeGl\\xf78\\x9cG\\x1dP\\x1f\\xc0\\xb6D\\xf1\\x8b\\xf5e\\xfd\\x95\\\\\\xae\\xc9\\xee\\n\\xc4\\xb1\\xc1\\xbc\\x94\\xe0\\xb6%\\x7fVq\\x05\\x01\\xeb\\xfa)\\xa7uvO\\x7f\\xe8\\xe6\\xef\\x94\\x9b\\xb7\\xaev\\x81\\x87+\\x10f\\xdd\\x97N\\x14\\xd6\\xe8\\xb7t\\xba\\x9d\\xde\\xf1\\xde%)\\xceg\\xdd[\\x8ewS\\x88\\xcc\\xdc$6\\xa2\\x06\\xf2\\x80\\x90\\x9e}=zT\\xb4\\xd8^\\xd8\\x8c!\\xcb\\\\l\\xb7\\xb2\\x98\\x9d\\xab\\x96\\xb6\\xd6\\xef\\xdb\\x95\\xdd<\\xa2\\xea\\xdc\\xb6.\\x96\\xd3#vB\\xc0A\\xde\\x81\\x99\\x12\\x0cWX\\xcd\\xaa\\xae\\xd0\\xf6c\\x04\\xc1\\xf6c\\x12\\xbd\\xc1\\xf0\\xc5\\xfc\\xe9\\xbb\\xcbd\\xbc\\x92\\xe3\\x85XZV\\x98R\\x0c\\x9c\\xc1Z`o\\t\\xf1\\x8a/\\x81\\xdc\\xcb;\\xc1\\xa4*|\\xab(\\x82\\xa8I\\xc8\\xe5O\\x95\\xa1oi\\x91\\x89\\xaa\\xc5\\x101rCH\\xcb\\x89\\x8c\\xfaSR\\xc1\\xb5\\x91n\\x8c\\xb8\\xe7\\x9f\\x95fD%O/a\\xa6*I\\x90t\\x03\\x8cS<3\\xa5\\xce\\xcd+\\x05\\x0b\\xbc\\x1bB\\x81\\xb8[@\\xb72\\xe0\\x01rw\\xa4\\xa0\\x12\\x04D\\x981\\xac\\x1c\\xc1q\\xfa\\x8bVH\\xb8\\xd8q\\xdd\\x17S\\x89\\xee\\x85\\x0e\\xf1)G\\x88\\x82I0F[\\xc9\\x1b\\xa0\\x11\\x92\\x88V@EoC\\xbb^\\x84\\xdb_l\\x8a1\\x0bE9gv\\xab`\\x94w\\xe9PQ\\x85}\\x1e\\xf1H>\\x90\\xde\\xefa&\\tIL\\x10\\xa1\\x9d\\xc2\\xee\\xfa(_y\\xab\\x8b\\xa7\\x1c\\xb7\\xb7E\\xbbD\\x80\\x96\\xd0\\xa2@\\x00D\\xe7\\x9eq\\xbd\\x1c\\'*=L.\\x08\\xb2y\\xcc\\x8a\\x11\\x00\\xe6\\'0xz\\x8d\\x13\\xefr/\\x87\\xd0\\x06\\x16\\x16\\xcbjL\\xee\\xa9 \\x89\\x10t\\xac\\\\\\x9a\\x85\\xcdg{C\\xa7\\x84\\x15\\xa4\\x15 \\xa9\\x10\\xa7R\\x93\\x13*\\xe4\\x04\\x9a\\x91;\\xce\\xaf\\xd1H@\\xe6\\xac\\xcf\\xb0}\\xf4o~\\x10wn\\x7f\\x1e\\xbfb~\\xea\\x93\\x84v\\xe6\\xd9\\xc61\\xdb\\xdc=L^X\\xb3l\\xe7\\xd1\\xd8^\\\\\\x17\\x15k\\xbc\\x94\\x92\\x01\\x98 \\xcc\\x825\\x11_\\xa2\\xe9\\\\z\\x98\\xf7\\xcb-\\xbe\\xb2ko\\x8f\\xd6\\xee\\xc7;,\\xb2OKw\\xa5\\xd6\\xc5b\\xe9\\xc1\\xdeZ\\xb1=\\x98\\xc3\\xb1\\xf6T\\xdaRZ|\\x04\\xa9\\x05\\'%%[\\xaa\\x13\\x99\\x04Fu\\xc3\\xed)\\x97_\\xa7%\\xd7\\x1f\\xe1\\xdb\\xec\\xfb:9[\\x8f\\xabF\\xc36\\xcb\\xb3U!\\t\\xc4\\xbb?v\\xc1\\xc4\\xfdd\\xd8\\xb5p\\x91\\xc7\\xd2A\\n\\x8fU|;\\xd1\\xcey\\x8f\\xaf>#~\\xafU\\x86b\\xbd\\x8e\\xde9\\r\\x9c\\t\\xa7V \\x8b\\xfbu\\xb2\\xaf)t\\x0eC\\x8ds\\xb8\\xeb\\xd3M|\\xdb}\\x7f\\xb3\\xd2\\xd8\\xec^\\xc5\\xe2iR\\xf0\\x96\\xad\\x14\\xc9\\xd7\\xf0U\\xfa\\xdbI\\xf3\\r\\xac\\n?\\x03\\xdf\\xee\\xb0\\x1b\\x01\\xb3\\xe9H\\xdf\\xb6\\xbeV\\xef\\xa2\\\\\\xc4nI\\x1eG\\xbc\\xabZ]\\xf6\\xfb\\x1c^\\xc4`n4\\xa6\\x9dj\\xf9\\xc6\\x14\\xa0\\xa2\\xd2\\xf1\\x0b\\x85\"y\\xc1^T\\xe9\\\\\\xab\\x9c\\xbeQ\\xf8>\\x1f\\x82m\\xd6\\x17o\\x85Z7j\\xc3\\x98b\\x16\\xa46\\x0c\\x15\\x07V\\x99\\xd7X\\x8a\\xdc\\x9c1n\\xd9\\xc6\\xe7y\\xbe\\x83\\x10F|\\xb3\\x15O\\xa8=wu|\\xfd\\xbb\\xa8\\xba\\xc4/\\xdfB\\xc6\\xea\\xd2\\xe5\\xdb\\x8a\\nL@I\\x05P@\\xe5\\xd6\\xb5.\\x99\\xed\\x8a\\xf7V\\xe3\\x8e8\\xb5\\xb8\\xf2\\xd6\\xe7\\xe5\\x14\\xa5\\xa9Eq\\xa6\\xf1:\\xc4q\\xd2\\xa9U\\x11\\x90D\\x134pJRaJH\\xcc@\\x89\\xaa\\xf0\\x00\\x02R\\xa5H\\xcb\\xaey\\xf2\\xa6\\xad*\\xf1\\x8f\\xc8\\xa0\\xe5\\xc7?U1\\'\\xb7\\x9d\\xba3\\x12\\x0f\\xd8(\\x9e\\xa3ej\\x08\\xcbY\\xd6\\xa3@\\x0f\\x12x\\x8f\\xdfN\\xd9\\xd0\\x9dH\\x0e-\"\\x00\\x04\\x81T\\xa7F\\xd6\\x12\\x12\\x0e\\xf1\\xde\\x9fF8s\\x9ah\\xd1I\\x00\\xc8\\xf8\\xd3\\xa0u\\x11\">5+\\x0e\\xb8@\\xb1|\\x14\\x82N[\\xdc\\xb29z\\xfe\\xca\\xc5\\xa9\\xf4\\x11\\x84\\xee\\xb2\\xd8\\x9d\\x12\\x07\\xba\\xb9Iu\\x1a9\\x9dj\\xda\\x82M2\\xd0:\\xda\\x12\\xa7\\xea\\xc7\\xae\\x8a\\x89(\\x91\\xe3Q=4\\x15TRR\\x00\\x84\\x80\\x07AF\\xb7\\x10\\xe9\\xd6\\x90R\\x9c[\\xda-\\xbe\\x10\\xc6)\\xf3{\\x1c+\\x18\\xc3\\xafZ;\\xaf7\\x89\\xaf\\xbc+oq=\\xd9L\\x92F\\x84D\\xc4i\\xc8~\\x93\\xe1>nr\\xe5\\x9eX\\xe5/\\x8d\\x7f_g\\xc4\\xf8\\xeb\\xd3\\xc2\\xc9\\x84\\xb2\\xcf\\x7f\\xff\\x00jN\\xc9`\\xcc^\\xdd\\xa9\\xb7\\x18X\\x01\\x9d\\xe0ZZ\\x90FcB\\rq\\xfbC\\x19\\x8e\\x1b\\x9e\\xee\\xbf\\x01\\x95\\xcb+>\\x8fY\\xfeJ\\xa0\\x08j\\xfe\\xfd\\xb9\\xe0\\xe1C\\xa3\\xfa\\xc9\\x9f}|~\\xe7\\xd5\\xed0\\xe6\\xca\\\\\\x91\\x08\\xb8\\xb2}<\\x9d\\xb7R\\x0f\\xb5$\\x8fu]\\xf4v\\xc5k\\xbb\\x16\\xb4\\xaf\\x7f\\xf0M\\xb2\\x97\\xafyip\\x94\\xab\\xde\\x12}\\xf4[\\x8d\\xf3\\x14\\x99OU\\x85\\xa8\\xda\\x0c-)\\xf9\\x961\\xb5xz\\x13\\xa2J\\x96\\xfbc\\xd5+O\\xba\\xb9\\xdc0ky-\\xac\\xbbA\\xdb\\x1b\\'\\x00;C\\x84b\\x11\\xfeo\\x10\\xb4\\r,\\xf4\\x94\\x94g\\xea\\xab\\xe5\\xcb\\xe2\\xae\\xfb\\x19\\xb7l\\x1bI\\x88\\xed>?\\x86\\xdeb\\xf6V\\xd6\\x97,\\xd9\\x86\\x87\\xcd\\x96\\xa5\\xa1\\xc4\\xf7\\x84\\x85\\t\\xd32F\\xa7\\xce\\xb3\\xad7.\\xdeQ$%fI\\x00\\x81\\x06:Ph\\x9dPS>\\x13\\xaeC.5y\\x1aE\\x8f\\x17\\x1a\\x85\\x19\\xc8t\\xa6N9CpC\\xa6L\\xe4>\\x02\\x9a\\x85\\xc2=\\xb9Q\\xf5\\x15[\\x8b\\xfeA\\x07RI\\xcfN\\x14\\xc4\\x9a\\x8c\\xd8F|xyU4\\x8b\\x03\\xa5\\x08\\xb4\\x05)\\xd6\\xd3;\\xc4\\xac\\x00:\\xcdZC\\xbbN\\xe5\\xe5\\xca\\x0c\\x8d\\xd7V\\nH\\xd2\\x14D\\x1aj0G\\x8a$\\xd3AI\\x88\\xfb9\\xd4N\\xa3\\x81\\xd6@\\xa9\\x9d\\x96I6\\xce\\xa4(\\xc1 A\\xe2t\\x9a\\xc5\\x9c\\xe8\\xef\\x87\\xd0R\\xa4\\xb4\\x90\\x16\\xa02\\x8a\\xa4\\xd4D\\xf7\\xabW\\xe4\\xdb1\\xcd~\\x11\\xf7\\xd5n\\x90n)\\x7f\\x94p\\x91\\xc99\\x0f\\xbe\\x89\\x96\\xc9\\xe1\\x90\\xad\\x80\\xa9\\x05H*AR\\n\\x93\\x81.\\xee^z\\xedj\\xb8q\\xd7T! \\xb8\\xb2\\xa2\\x06\\xb1\\x9f\\x0c\\xeb\\xf6w\\x1cp\\xce\\xccf\\x9f\\x93\\xb9e\\x9e;\\xca\\xed\\xa9vSv\\x9b\\x8cuH\\xdd\\x83\\xf3e\\xfa\\xe0\\x8a\\xf9?kMt\\xbf\\x8b\\xea\\xfd\\x93\\x96\\xf3\\xbf\\x83`e\\xb4\\x107\\x92\\x93\\x97\\x115\\xf9\\xdbk\\xef\\xc8p\\xd8[/\\xd2a\\x13\\xe5Wu\\x8b\\xb6\\x10\\xac\\x1e\\xd5C$)>J\\xab\\xe6Vn\\x10C\\x07m>\\x83\\x8b\\x1eb\\x9f\\x98;\\x08\\x7f\\x08.$\\xa5Jm\\xd4\\xf2Zd{\\xe9\\xef\\x95v0\\x1e\\xde\\xacSa\\xb4\\x98:\\x10\\xdbm%v2\\x03`\\x01\\x93\\xca\\xe04\\xd4\\xfbkX\\xd9E\\x9axA\\xf93:n\\x8f^T\\x01$}\\x04rV~\\xca\"\\xa6JA \\x0fm+`A\\x83\\x03.>U\\xad\\r\\xe8\\x95\\x15)EK\\xcc\\xe4\\'\\x98\\xd2\\xab9\\x01\\xea\\xce\\x90\\xae\\xc6\\x07\\xd0#X\\x93\\x99\\x1d()\\xcdf\\xc2\\t\\x9dyt\\x15Dp\\x01\\xb8\\xa2})\\xca\\x9dz\"\\x0f\\xa5\\x900*\\xf0\\x06\\xe1R\\xd6\\xa5\\xadEKQ*R\\x89\\xcc\\x93\\xc6h\\x84\\x95\\xa4\\xefG\\n\\x87 \\x06PxS\\xca>AJ\\xc4\\x80\\x0f!P\\xa4d\\xa6\\x16\\x9dd\\xc4s\\xaeyl\\xbe\\x84\\xb6\\xd2\\x1b\\x10\\x84\\x81\\x00\\x0e\\xb9\\n\\xb6\\x8b\\x9a.H*\\x96\\x91\\xd7@\\x15 \\xa9\\x05H*ARq/hB\\xd2\\xde\\xfd\\x8bD`\\x18v\\x17z\\x90\\x1eS\\xd8u\\xe9}\\x97\\x9bP \\x089\\x02\\x08\\xd4\\x1e|\\xc5~\\x9b\\xe12\\xcb+r\\xbdK\\x94\\xfa\\xcdW\\xc5\\xf8\\xfe\\xc9&3\\x19/\\xbc\\xbc=\\x0fd+\\xff\\x00\\xa4\\x82u6\\xce\\x89\\xe3\\xc2\\xb8}\\xad\\xfb\\x9f\\xe3\\x0f\\xd9?\\xbd\\xbf\\x83p\\xb6W\\x84r\\x8a\\xfc\\xd5~\\x86& \\x8a\\xc9<\\x08\\xa9\\x0ejB$T\\x9c\\xf5\\xf2\\x923\\xb4X\\t\\xcb\\xf3%\\xa7\\xfbz\\xe9\\xd2\\xe7ny\\xb3d\\x9f\\x08?\\xaa\\x0c\\x0f*\\xdb \\x99\\xee\\x13\\xd1Y{(\\xf4\\x1ey!D\\x12D\\x8c\\xb2\\xaa\\x11H\\xe9\\xed\\xa4\\x1b\\x90\\x01\\x83\\xea\\xa4\\x00R}@Ub\\x91[\\x8d\\x10m\\x91\\xa0 \\x9c\\xf8iL;Og&\\x139G1\\xd2\\x89\\x07\\x1a,\\xc1\\x19)1\\xee\\xa7`\\x9c\\x8a\\xb8\\x03\\xd2\\x8f\\x06\\x01 \\x11\\xe2MZ\\x02W\\xed\\x0c\\xfa\\xd3\\x11\\\\F\\x86j\\xe4\\x9f\\xb6io:\\x946\\x92\\xa5\\x98\\xc8\\x0e\\xbf\\xbe\\xa0h&\\tB\\x81\\x07\\xbcH \\xfe\\xd0\\xaey\\xfb\\xa7\\xd0\\xbe5_(+9\\x10\\xaa!\\xd7P\\x15 \\xa9\\x05I[\\x8cc\\xd8N\\x0c\\x82\\xacW\\x12\\xb3\\xb3\\x11 <\\xf2RO\\x90&O\\xaa\\xb3r\\x93\\xcdO.\\xae\\xd5\\xf68(\\x8f\\xc2\\x8e\\x98\\xe2,\\xdf \\xff\\x00R\\xb1\\xf3\\'\\xb5:\\xaeD\\xda<\\n\\xf7\\x01\\xc4R\\xc5\\xf3l\\xa1N\\xb6\\x1emL\\xbc\\x97P\\xb4I\\x12\\x14\\x92F\\xa9=k\\xf6\\x98u\\xf0\\xeb\\xef,?\\xac\\xd7\\xf7~c\\xab\\xd0\\xcf\\xa1\\xacs{\\x0e\\xc9\\xae\\x126\\x99\\x990\\x0b.\\x0fu|\\xef\\xb52\\x9f&\\xfe1\\xed\\xfb*\\x7f\\xbb\\xfc+r\\xb4\\xb8l\\xa4x\\xc0\\xca\\xbf5k\\xf4RT\\xf6\\xddI\\xd1I\\xf6\\xd0\\xb4\\x90\\x95\\xc8\\xc8\\x8fm\\x08{\\xd5!\\xefe\\xad\\x07N}\\xf9H\\x1f\\xe1\\xcc\\x04\\xff\\x00\\xdd^\\x04\\x7f\\xaeMv\\xe9z\\xb9\\xf5=\\x19\\xba$\\xb6\\x9c\\xc7\\xa0\\x9f\\x85n\\xb9\\x87\\xf9\\x90\\x00\\x00\\xef\\x0f\\xee\\xd0\\x8c\\xf1\\xd7\\x8f\\xb6\\xa4%g\\xf7\\x83N\\xd1W@%\\xff\\x00\\x0c\\x0f\\xa2l\\xff\\x00TR\\r\\xa67VHQ9n\\x90\\xae=}UpU\\xb8\\xc6V\\xe8\\xe7\\xbcr\\xf5S&\\xd9Nk\\xf3t\\xf9\\x8f\\x80\\xa2S\\xa2\\x88\\xe2L\\xfcj\\x88\\xe5\\xa0\\x9b\\xcbq\\x1f\\xe7\\x12?\\xac*\\xd24\\xb27\\x94\\x00\\x80\\t\\xca\\xa9\\x10\\x94@B`\\x19\\x12J\\xa7#\\xf7V\\xa0)\\x04\\x84\\xab@8\\xe7\\xadI;\\n|[^%\\xd2\\x01\\x01\\'\"\\xa8\\x9c\\xc5\\x1bU\\x15\\x0b\\xef_\\n\\x80\\n\\xdeFZ\\xea\\xa1\\\\\\xf3\\xe5x\\x8f\\xa1\\\\j\\xa4F\\xb1P\\xe9\\x88u\\xd4(v\\xbbjl6Z\\xd9\\x87\\xb1\\x14\\xbe\\xbe\\xfde\\r\\xa1\\x94\\x02T@\\x93\\x99 \\x0fY\\xac\\xe5\\x97i\\xd6\\xde=[s\\xb58\\xc1\\xdd\\xd9\\xbd\\x96q\\x089%\\xfb\\xa9 \\xf5\\xfa\\xa9\\xfe\\xb9\\xae_3+\\xf7a\\xed\\xf7\\xa6\\xbf\\xc9m\\xba\\xc7H8\\xf6\\xd0&\\xc5\\x83\\xab6\\xae\\x10@\\xe4Ca\\x1e\\xf5\\x1a\\xbb3\\xcb\\xcd?\\xb3\\x13\\xf0\\x8e\\xc96z\\xcda\\xcb\\xbf\\x9c^\\xbcsQR\\xfb\\xb0\\xa3\\xfe\\x8c\\x13\\xeb&\\xb7:rM.\\xff\\x00g\\xa6N\\xc7\\xec\\xf2R\\x120k(\\x023h\\x1a~^>\\xc3\\xbf/w\\n<B\\\\0 @\\xaf\\xddu9\\xaf\\xc8\\xe3\\xcc\\xe5\\xeb\\xbb-s\\xfe\\x93\\xdb\\t\\xd5\\xb7\\x7f\\xbb_\\x13\\xed?\\xdc\\xdf\\xe0\\xfa\\xdffMu\\xbf\\x85m\\xf6\\xaexFu\\xf9\\x9a\\xfd\\nknVI\\xd0\\xe1\\x1cjP\\xa1p\\xb1\\xa2\\xd4\\x07\\x9d[B7\\xee\\x8c\\x92\\xe1\\xf34\\x8a\\xc7>Q\\xc8O\\xcf\\xf6e\\xcc\\xe5\\xcbW\\xca\\xba\\x9e\\xf5\\x1f}w\\xe9z\\xb8\\xe7yf\\xcd\\x9f\\xa3F\\x9e\\x80\\xad\\xd6\\x01\\x07\\xe8S9\\xe6?\\xbbUFU\\xaeS\\x04\\xd5\\xe8\\x07\\x902>\\xea\\x9a\\x0b\\xa2\\x14\\xe2JU#\\xbb@\\'\\x91\\t\\x15\\xaa\\xcd\\x86\\xb2\"~\\x19\\xc5e+\\xb1x6\\xa9\\xcb\\x89\\xf8V\\xb6\\xb6\\x9e\\xd7\\xe6\\xe9\\x8ec(\\xe9DE\\xee\\x93\\xbcxq5/\\xa2N\\x0e[\\x18\\xd6\\x1e\\xab\\x85\\x040\\x9b\\x96\\xbb\\xc5(d\\x94\\x85\\x89\\'\\xa4S<\\x8a\\x86\\xaf\\xca*3;\\xca\"4\\x89?\\xba\\xa4r\\xda\\xdd\\xeb\\xbb\\xb6\\xed\\xac\\xd9q\\xfb\\x87\\x15\\xba\\xdbM\\r\\xe5,\\xf4\\x14\\xc8\\xce\\xde\\x92\\xebc\\xef\\x19\\xb6\\xb2-\\\\[=t\\xf2\\xd4\\x97\\x19K\\x89\\x1d\\xd0\\x98J\\xb3\\xcc\\x89\\n\\n\\xcaR\\xa4\\x91\\xd6\\xb5\\xa3\\xb7\\x9e\\x12\\x95\\xee\\x90B\\x92`\\x89\\x98 \\xc7\\xdf\\\\\\xef\\x92n\\xcf7\\xd9N\\x7f\\x9c6?\\xae\\x9a\\xc6I\\xf44\\xd6m\\xe4\\x92\\xa3\\x98\\xac\\xd2\\x01UD]wd\\xcb\\xec2\\xeb\\x8d-\\xd6[Z\\xdb$\\xa1JH%\\x07\\xa1\\xe1X\\xcc\\xc3\\x9b\\xd5\\x9e\\xfa\\xb4\\x1b\\xc6\\x8e\\xfa\\xb4-\\xe3Wu:\\r\\xea7V\\x9f=]ZK\\xbe\\x15\\x02 z&k\\xf7\\xddO/\\xc9\\xe1,\\x9c\\xbdOfJ\\x8d\\xa7\\xb4\\x99\\xcc9\\x19\\xfe\\xa9\\xaf\\x89\\xf6\\x97\\xeer}_\\xb3\\x7f}\\xfc\\xdb\\x85\\xa2\\xce\\xe8\\xaf\\xcdd\\xfd\\x04MmyVIjt$g\\xec\\xa0\\x18S\\xa5}\\x05+cB\\xb3\\x14\\x86_\\xf2\\x8a\\xff\\x00\\xedU\\x7f\\xdd\\xeeG\\xf5\\xdb\\xae\\xdd)\\xe5\\xcb\\xa8\\xcd\\x1a2\\xca\\t\\xd3ph+\\xa5\\xf2\\xe6P\\xca\\xd9#z|@\\xfb\\xa8\\xf2L\\xc6z\\xf1\\xca\\x99\\x15\\x023\\x92ds5x\\x16\\x90FRU\\xd2k^\\xa0\\x08\\x81\\x19\\xc05\\x94\\xad\\xc6D[ \\x80=.}*\\xf4I\\xac\\x19\\xb7G\\x0c\\xc1\\xcf\\xca\\xa9\\t\\xe2a%?\\x1a\\x81\\xb2\\x0123\\x9c\\xf5\\xa7~\\xe8Y\\x1a\\x92\\xc7\\x03\\xc5]\\xc2o\\x14\\xf2\\x13\\xde\\xb2\\xebe\\xb7\\x99\\xdf(\\xef\\x13\\xa8\\x85\\x0f\\x12HP\\n\\x04A\\xcb\\x914\\xcb\\xdbY\\\\\\xe2[m\\x7f|\\xa5,[[\\xb4\\xf2\\x81\\xdfY\\x95x\\x8cf\\x91\\xc0e\\xa6y\\xd3\\xdd\\xa5\\xa7\\x99\\x05Jp\\xa9J*R\\x8e\\xf2\\x94\\xa3\\x99$\\xeaz\\xd6g-x\\x16\\x1d\\n\\xbc\\xb6\\x11\\xad\\xcbC\\xfbD\\xd7<\\xbdV\\xdfB\\xce\\xa6\\xb1\\x97\\x92B\\xf5\\x15\\x92!R=^\\x86Hp\\xc4W<\\xcc&k\\x99\\t\\xa8\\x84\\xd4\\x82jN\\x13\\xda\\xfc~\\xfb\\x18u\\xabk\\xe4ad\\xdb8\\xb5%\\xfb\\x16\\x12\\xd0t\\xa8\\x00U)\\x80\\xa0wA\\x19\\x03\\x9d~\\xd3\\xa5\\xf0\\xd8tov;\\xe6x\\xb7z~\\x7f\\xe2>\\'.\\xaf\\xec\\xe5\\xae=\\x8evl\\xa5\\'j\\xed\\x010!\\x7f\\xdd5\\xe0\\xfbF\\x7f\\xb3\\x93\\xbf\\xd9\\xd7\\xfd\\xe9?\\x16\\xe1f\\xaf\\x00\\xce\\xbf3_\\xa1\\x89\\xa8u j$r\\xac\\xe9l\\x8d\\xf2L\\x93Z\\x1b\\x184-\\x9cA\\xce\\xa4\\xcd>P\\xf9\\xdal\\xaa\\xbf\\x93\\xba\\x13\\x1f\\xac\\xddw\\xe9z\\xb9f\\xcc\\x99\\xfc\\x8bG\\x9a9u\\xae\\x96r\\xe4\\x083n\\xa9\\x83\\x0b\\x03\\xddY\\xbeZ6Vg\\x87\\xf8\\xebJ\\x02\\xa3:\\t\\xa8\\x02\\xc2\\x91\\x01@B\\xd0\\x1c\\x11\\xc4\\x1d*\\x02\\x85()A;\\xc1\"I\\t\\xf8\\xd0U\\x98\\xcf\\xe6\\xc9\\xde\\x80\\x02\\xa7\\xddN\\xc6\\xbdS-\\x89\\xf9\\xb2\\x0c\\xc1\\xcb!\\xe5LGJ\\x8ec\\xec\\xa5\\t2\\\\@&$\\x811\\xd7Z,PGxH\\xd4\\x89\\x07\\xe1TW\\xc8\\xd4\\x95\\xee%d(!D\\x80c,\\xbf\\xe7U\\x12\\t>,\\xe7:}\\x11cQ\\x19d\\x04\\xd0\\x83\\x08\\x8f\\xc2v \\x9c\\x8d\\xe3\\x1f\\xefQ\\\\\\xf2\\xbcS\\xe8\\xfa\\x16k7\\xc96\\xb1\\x98\\xac\\xe8\\xc1r\\xa9\\x1e\\xae\\xec\\x9ax\\x80D\\xd7,\\xcc7\\xbc8\\x1a\\xc1\\x1c\\x9eG\\xd9Q\\t<\\x8f\\xb2\\xa5\\xb1\\xc1\\xe4k=\\xd1>|\\\\\\x83\\xbe\\x0fJ\\xfe\\x81\\xd4\\xf2\\xfc\\x9e\\x1e\\x17}\\x9f,#j-7\\xb4\\xf1\\xff\\x00t\\xd7\\xc7\\xfbC\\xf79>\\x97\\xd9\\xff\\x00\\xbe\\x9f\\xc5\\xb3\\xda\\xbe\\xa5$\\x00xW\\xe6\\xb2\\x8f\\xd0J\\x9c\\xd1\\xebB=5\"\\x81\\xa1\\x1cA\\x829T\\x19\\xbf\\xca\\x13<7eU\\xc8\\xdd\\x8fsu\\xdb\\xa5\\xea\\xc6l\\xce\\xdc\\xcb\\rg\\xf5>\\xd3[\\xae`\\xdf\\xe4\\x15?\\xa4\\x91\\xa7CV\\x91\\x99\\xea&u\\xa9\\x00\\xd4e\\xea\\xab\\xc2J\\xc5\\x1b\\xee\\xce\\x1f\\x04\\xfd%\\x8b\\x0b2x\\x9d\\xe9\\xf5eZ\\xca\\x04P\\xa2\\x94\\x98\\x98\"5\\x89\\x15\\x9f>U\\xe1[\\x8c\\x89\\xb5N\\x7f_\\xcb\\x85>\\x12e\\xaf\\xe6\\xc8\\x1cde\\xea\\xa3h\\xe2\\x8eY\\x9e\\x15J\\x8ak\\xf3\\x86B\\x89\\x00\\xad3\\xa71LBy >\\xf6r\\x03\\x8a\\x1au4\\xfa\\xadz\\x90\\xa4\\xea2\\x91\\xca\\x8d\\x8d\\x15\\xc72i\\xf4%\\x1fI3\\x91\\xdd\\x1a\\xd5\\x15\\xa5`\"q\\x9c0\\x01\\x91\\xbe\\xb7\\x1f\\xda\\xa2\\xb9e\\xce\\xd7\\x0f\\xa1\\x1ck\\x17\\xc9%DNul\\xe8$M<\\x03\\x95\\xd4<OhX\\xb20\\xeb\\x8b\\x04\\xbb\\x89\\xdf\\xd8\\xa5hqa6\\xae\\xdb\\xa0<AO\\x85E\\xd1#\\\\\\x8ar\\xcc\\xcf\\n\\xeb\\xd2\\xc3\\xbb\\x7f\\xb3\\xbf\\xe1\\x95\\xfe\\xdf\\x9b\\x97W-k\\x9d\\x7f\\x19?\\xbb?sjY\\x0by\\xb71lI\\xe6\\x1c\\x08\\x85/\\x1dm\\xb7Z +{w\\xba\\x10A\\xf0\\xeb\\xd7M\\x0f\\xaf\\xe4_3\\xa7w\\xff\\x00\\xf1u\\xfd^_\\x99=s\\x9c\\xff\\x00\\xdd?$\\'1\\xfc)\\xd4).\\\\\\xe2\\x0e\\x93\\xe1\\x976\\x8e\\xed{\\xb39\\x80\\x84D\\xe9[\\xf9\\x1dYw:\\x7f\\xfaa\\xf9\\xe4\\xcf\\xcc\\xe9\\xf8\\xb9\\xff\\x00\\xed\\x95\\xfc\\x9eu8\\x06%v\\xcd\\xba\\xdc\\xbd\\xc4.\\x83\\xe8J\\x90\\xb4[^:\\x97\\x02\\xb2\\x04\\x18\\x02\\t\\xafM\\xeb\\xe3\\x85\\xb2t\\xa4\\xd7\\xbd\\xc2~n3\\xa1r\\x92\\xdc\\xf7\\xbf\\xa6T\\xa1\\xb1\\x17\\xcb\\x01B\\xd7\\x14!Y\\x8f\\xe0\\xa7\\x87\\xc5U\\x9b\\xf1\\xba\\xff\\x00\\xeb\\x8f\\xfex\\xff\\x00\\x86\\xbf\\xd2\\xcf{\\xff\\x00\\x8d\\xff\\x00,Q\\xd5-B`o\\x01\\x96U\\xf5\\xf3\\xca\\xd9\\xb7\\xcb\\xc6H\\xb3\\xd8\\x82\\xb1\\xb4\\xb6\\xbb\\xc0\\x03*\\x064\\xd0\\xd7\\xca\\xf8\\xcb\\x97\\xc9\\xbd\\xde_O\\xe0\\xb5\\xf3\\xa7kf\\xb0\\x90\\x81<\\xab\\xf3\\xf9W\\xde\\x8bF\\x8f\\x87:\\xc2;4!\\xa5U\\x13\\xa8Vt\\x06y\\xdb\\xff\\x00\\x8b\\x03\\xd9\\x82 \\xfd-\\xd2\\x7f\\xa8\\x83\\xf6Wn\\x9b\\x9el\\xc1\\x83\\xf4\\rH\\xd53\\x1e\\xba\\xe9yb\\x8d\\xb3,*\\x0eeI\\xcb\\xd4h\\x06\\xa3A\\'R5\\xccU\\xb3@\\xe4HN\\x9c\\xa9\\xe0\\x15p\\xf2\\xdf\\x0cw\\x9b\\xbfD\\xcaXD\\x0f\\xaa\\x92H\\xf5\\xf8\\xaa\\xb7\\xd0kD\\x02#\\xac\\xd1\\xc9W\\xe2\\xe3\\xf1D\\x90>\\xb7\\x1f*N\\xd2\\xed\\xb3\\xb6F\\x9fW\\x86\\x99Q \\xa7\\xe1;\\xae\\x12s\\x07\\xc3\\xf7\\xd3\\xf8\\x02I\\xdct\\x14\\xe6B\\x81\\x04\\xf1\\x8a\\xbdI.\\x19qJT\\xca\\x89Q\\x00s\\xa9\\x02\\x87\\x8c\\xf5\\xa7\\x940\\x01\\x19\\xe9>F\\x80Z\\xb7{\\xc1\\xb8HH\\x027\\xb2>\\xba\\xa5\\xa4\\xbd\\x98N\\xfe\\xd1\\xe0\\xc8\\xe2q\\x1bQ\\xfd\\xb2+\\x15G\\xd0z\\xcd\\x88\\x85\\x8c\\xeb:h\\x82\\x0e^b\\xa8\\x8f\\xd7fY/mX\\x16)\\x8c\\xe2\\x989\\xc3-\\x83\\xc8e\\x87\\x82\\xd4V\\x13\\x05JDk\\xe4k\\xdf\\xf0_\\x15\\xd3\\xf8y\\x94\\xce\\xf9\\xd7\\xa6\\xfd\\xdeO\\x8a\\xf8|\\xfa\\xda\\xec\\xf4\\xdb>F\\xc0\\xed2\\xe3\\xf1FR?Z\\xe55\\xeb\\xbfk|<\\xf7\\xfeO/\\xff\\x00\\x1b\\xd5\\xbe\\xb3\\xf9\\xa7\\xd9vo\\x8f\\xae\\xe1\\xa3t\\xdd\\xa0\\xb7\\xdfIp&\\xe7\\xc7\\xbb9\\xc6Z\\xc5r\\xea}\\xaf\\xd3\\xed\\xbf.]\\xeb\\x8d\\xce7\\xe9\\xbe]0\\xfb3>\\xe9\\xddf\\xbf\\x1f\\xf8z\\xdc/g\\xf6\\xc2\\xc1\\x86\\x99\\xb3\\xb9\\xb2f\\xdd\\xa6\\xd4\\xcbm+\\x12}hB\\x0f\\x007Fc\\x81\\xd4e\\x15\\xe0\\xea|OK\\xa9m\\xcbv\\xdf\\xfbp\\x9f\\x9b\\xdb\\x87\\xc3\\xe5\\x87\\x18\\xeb^\\x9c\\xe5JN\\xcfm\\x82R\\x121[`\\x00\\x8c\\xf1\\x1b\\xc3\\xff\\x00\\x15_\\xea:>\\xd7\\xff\\x00\\x1c?\\xc0\\xff\\x00O\\x9f\\xbc\\xfey\\xff\\x00\\x96@\\xad\\x98\\xc3\\x14`\\xd85\\xed?}}O\\xf59\\xfb\\xbcS\\xe1\\xf0\\xf6#\\r\\xc00\\xfb<P\\xba\\xc5\\xaa[q\\xb3\\xe1V\\xf1\\xcaG\\x9dy>#\\xaf\\x9eS\\xb6\\xde\\x1e\\xaf\\x87\\xe9a\\x8d\\xdc\\x8f_h\\x80\\x10 W\\xcd\\xcb\\xcb\\xdd\\x13Q\\xa5b\\x98p\\x1d(#\\x07\\xa5\\x1aT\\xe2O:\\x85x.\\xde\\x93\\xbd\\xb3{8y]\\\\\\x8d\\x7f\\x93\\x07\\xec\\xae\\xbd63e\\xb6\\xe0\\xf7-e\\xf5N^\\xba\\xe9\\xeb\\xb6\\nA&\\xd9i$F\\xf2O\\xc6\\x80A\\x80x\\'.t\\xc4Q\\x8d$O*\\x11$\\x0c\\xf4\"\\x94\\x00\\x89\\xcc\\x93\\x9ey\\xd5y\\xe5+\\xf1\\xa2\\r\\x9as\\xcf{\\x9fCBJ\\xb3\\x8f\\x9b7\\x9c@\\x19\\xfa\\xaa\\x91\\x1e$A\\xcf?:\\x91$\\x89\\x80u\\xe5J\\x02@\\x8c\\xfc\\xea\\xf5B\\xcb!\\x91\\xca\\x9f\\x08=\\xe2\\x84q\\xd2K\\xbb\\xcaV\\xf2\\x88\\x04\\xa8\\xf1\\x91D[?\\xb1\\xa3{kv}13\\x8a\\xdaG/\\xcb\\xb7B\\xf4}\\x01\\x1dj\\xd2E\\xc5\\x1cq\\x8c>\\xe1\\xd6H\\x0e!\\x04\\xa4\\x919\\xd6/\\x13f\\x18]\\xb5\\xea\\\\IN JB\\x84\\xa4\\xb0\\x93\"s\\x15x\\xa9e\\xc2\\xba\\x05\\x1e\\xd27~\\xe2\\x13\\xf81\\xc7\\x10\\xf8\\x1a\\x04\\xa0\\x82$N\\xbf}p\\xeae\\xac\\xa4t\\xc7\\x1d\\xe2\\xaf\\xc2\\x1a\\xc6Y\\xb8Q\\xc5\\x12_eI\\x88Sh\\x94\\x91\\x9ePx\\xd6{\\xb5\\xe6\\x19\\x8e\\xfdN\\x17\\x1eeeWji\\x1cCA\\xb4\\x95+\\xd5\\xc2\\xb9w_.\\x93\\x18q\\x97\\x1f}eJi\\x94\\xa4\\x9e-\\t?u]\\xd5\\xae\\xc8\\x94\\x12\\xe4h\\x8f\\xe8\\n\\xb7Wn,58s\\xca\\xfa\\xa9\\x1ek\\x15\\xf6/W\\x1fw\\xcf\\xf9Y\\x18\\x18E\\xd2.\\xdcX@)&ANcN\\x95\\xc7>\\xa4\\xbe\\x1dp\\xc2\\xcf)\\xcc\\xd9\\xbe\\x80%\\xb5\\xfb+\\x85v\\x87\\xd3l\\xf6C\\xb9s\\xfa5\\x9aJ\\x16\\xef\\x7f\\x12\\xe7\\xf4h\\xd2\\x1f\\xcd\\xde\\xfe%\\xc9\\xf2\\xabH\\xe2-\\xde\\x07\\xf2.{*\\xd0x>\\xdd\\x04l\\xb6\\x06\\x93)Zo\\x1e\\x05\\'P\\x0b_\\xba\\xb7\\x87\\x963e6\\xa4w\\ry\\x1e=k\\xa7\\xa3\\x14\\xa6}\\x07c8#\\xed\\xa3\\\\#J\\x99:\\x90j \\x0c\\x11\\x95@\\xf5\\xd2R\\x84Z\\x10\\x12;\\xcbd\\xa9P\"N\\xf2\\x84\\x9f`\\xaa\\xceN\\xfd\\r\\x08\\xdc^\\xf4\\xcce\\x1c\\xe9\\n\\xecX\\x9f\\x9a\\x82D\\xf8\\x86G\\xc8\\xd0w\\xa4\\xbb_\\xcd\\x1b\\x19\\xfd\\\\\\xe7\\xa5_TsA\\x1a\\n\\x06\\xd30[t]cxm\\xb3\\xb94\\xfd\\xdbM(\\xc6[\\xaaX\\x07\\xcfZ\\xde3wJ\\xddM\\xa1(%%h\\x00\\xf8V\\xa4\\x8c\\xf2\\x80\\xa2(\\xd7+cP\\x1b\\x89\\xd4+9\\x9f\\xb2\\x8d\\xf2\\x84\\x04\\' \\'9\\xad^Q\\xc7D,~\\xc2~\\x14Av\\x97\\xb0\\x83{mvdF\\x7f\\x85\\xec\\xe4\\x0f\\xe7\\xdb\\xaey]\\x19\\x1d\\xfe+i\\x0b\\x18P\\x16JJ\\x86N\\x10\\x83\\xd2\\x8b74g\\x95}\\xde\"\\xcd\\xe06\\x9b\\xc1\\xb7\\x14R\\xa1\\x0b\\x04\\xe4A\\xd3\\xd5N;\\x97ab\\x9b\\xd0~\\xa7\\xbe\\xad%\\x1e=\\x80\\xd8\\xe3wF\\xe6\\xe9O!\\xd1n\\xabt\\x94\\x11\\t\\x05@\\xc8\\x9e2+\\xd1\\xd2\\xf8\\xae\\xb7G\\x0b\\x87J\\xeb|\\xf8\\xe7l^\\x9e\\x19Y\\x96Sb\\xc20+<-\\x94\\xb3h\\xb7\\x10\\xc8B\\x92\\xa4\\x01\\x01JQ\\x04\\xab]r\\xd3\\xadx\\xfa\\x98u:\\xb9\\\\\\xfa\\xb9\\xdc\\xad\\xf7\\xd3\\xbe9\\xe3\\x84\\xed\\xc3\\x1dD\\xa6\\xf0\\xebv\\xcc\\xa5k\\x9f!X\\xff\\x00O\\xf5o\\xe7}\\x12K\\xd6v\\xc1\\x01\\xd5!\\x05D\\x00V\\xe4I\\xae\\x93\\xa5<i\\xce\\xe7m\\xbc\\x94\\xa4\\x8d\\xe3\\xe2X\\xe9\\'\\xef\\xa7\\xb2{E\\xb6#\\x94\\xd0\\xda\\xe7\\x0c3h\\x8c\\xf8\\x9aRX9kR\\x1f\\xae\\xa0)\\xebB\\x1e\\\\\\xe8%\\x03\\xfe&\\xa1YOo\\xb9\\xe1\\x18a\\xcf+\\xa39\\xf3iU\\xa9\\xc5f\\xb2[<\\xed\\xda\\xcf\\x81\\xe9\\xc6\\xb5-`\\xb6\\x7f&\\xe99z?\\x13O\\xd0\\x90AQ\\xf31V\\xd6\\x80\\x8d\\xd9\\xc8\\xc89\\x8a/\\x90z\\xf5aL\\xd8~\\xa5\\xa8B\\x87P\\xb5\\x9f\\x81\\x14\\xe5T0\\x06G\\\\\\xb45\\x15~/\\xf9\\x9f\\xfaY\\xf2\\xd0\\xd0*]\\xb9\\x9bDFY\\'\\xe1TGBg0%<}UD\\x97\\x81\\xba\\xcb\\x18\\xfe\\x18\\xf3\\xea(\\xb7n\\xf1\\x95\\xb8\\xa1\\x99J\\x03\\x89*=`I\\xadcu`\\xbe,Dt\\x80\\xfb\\xa4\\x19\\x05\\xc5\\x90G\\x11\\xbck6\\xee\\xd3H9t\\x14\\xa1\\x9fGN\\x15\\x0eKx\\xfd.C=\\xc4\\xf0\\xfdQT\\xbe\\xe9?\\xb3\\xc8V\\xdfl\\xaag\\\\b\\xceG/\\xa6Ec.a\\x9e]\\xf9ZJ\\xbd\\xa21b\\x8f\\xe7\\x07\\xdbQ\\x8c\\xab\\x17\\xdb\\xdd\\x9e\\xc0v\\x9a\\xe6\\xc7\\x18\\xc5]\\xb7y\\xa2\\n\\x9b\\xf9\\xab\\x8e$\\x12\\x12D\\x14\\xa4\\xf0 \\xfa\\xeb\\xa62\\xf6\\xf02\\xe2\\xf2\\x92\\x9e\\xd6\\xf6\\x1c\\x0c\\xf1\\xf5\\x0f\\xf6+\\x8f\\xfd\\x94\\xf6\\xe5\\xec\\xce\\xe0\\xc7k\\xdb\\n5\\xda \\x07[G\\xc7\\xfc\\x14\\xcc2\\xbe\\x8bp\\xa1\\xda\\xf6\\xc3\\x7f\\xfb\\x1b~\\xbbg\\xbf\\xf6S\\xf2\\xf2\\xf6[\\x85\\x7f\\xf1oa\\x95\\xa6\\xd2\\xdb\\xff\\x00\\xe4\\xbb\\xff\\x00\\xb6\\x8f\\x97\\x96\\xb7\\xa5\\xb9\\x12\\xb0=\\xab\\xd9}\\xae\\xc6\\x9b\\xb2\\xc2q\\xa1wr\\x10\\xa7\\x03M\\xa1BR\\x9c\\xc9;\\xc9\\xca\\x9de\\x8c2\\xcbx{\\xf0H\\x19Mq\\xd3l0\\x1a\\xc3\\xa2\\xe3\\n\\'\\xe6\\xa2x(\\xd2\\x13A4-\\x809T\\x82r\\x12j\\x03\\xa1l`\\xd2\\x99\\x7fn\\xe2p[\\x03\\x9eW\\x89\\xff\\x00v\\xba\\x99\\xdb!\\xb2\\x9e\\xe1\\x13<u\\xf5V\\xa3\\'Z0\\x87\\xa4\\x83\\x04x}u\"c>\\x934\\xf0h+\\xc4fu3V\\x81;\\xa2L\\x9f]\\x14\\x86\\xeeZ\\xcdZH8\\xa8\\x8bI\\xfda\\xc3\\xa1\\xaa\\x84\\x9bC6\\xcd\\xc1\\x13\\t\\xe3\\xd2\\x89=\\xd1\\xf0|\\x04\\x12L\\xf1\\x8d)\\xd1$\\xe79\\xeb\\xccSh\\x82)\\x92#Z4D\\xa4\\xceun\\x80\\x90\\x92%Q\\x07%\\x01\\x98\\xeb\\xe7O\\x00\\xab\\x93\\xf4\\xca;\\xdb\\xde\\x11\\x9c\\x1c\\xf2\\xaaOCV=\\x9cg\\xda\\x16\\xc9\\xc9\\xff\\x00\\xb6-8\\x7f*\\x9a\\xc5\\xf2\\xa3\\xbe\\xabIS\\xb4\\xa6,[\\xfet|\\r\\x069c\\xb5WT\\xd6\\xde\\xed\\x1b\\xad\\xa8\\xa4\\xa1\\xebq\\xbc\\x0cG\\xd1 |+\\xdd\\xf0\\xd3x<\\xff\\x00\\x15\\xbd\\xcd=\\xee\\x1b\\xb1I$)8\\x96\"\\xd4\\x8f\\xe3\\x8f\\x1ar\\xf8\\xac\\xa7\\xff\\x00\\x8fu\\xf8n\\x8e\\x17\\x8d\\xff\\x004\\xec\\x1f\\nU\\xcd\\xd8\\xb27wM\\x9b{T\\xa9D9\\x9a\\xfe\\x95Y\\x9c\\xb3\\x91OS;\\x8e\\x13?{\\xf9)q\\xc7+\\x8c[\\x9c\\r\\xb1x\\x96\\xcb\\xd7\\x1b\\xa1\\x0bp\\xa7|\\xc1\\x81 \\xe9\\x91\\x15\\xc7\\xe7e\\xdb\\xb6\\xac\\xc6\\xd3\\xf6\\xdb8\\x97\\x92\\xcf\\xce6s\\x1b|+%:\\xe6\"\\xd91:\\x90\\x08\\xf8\\r+_:\\xcf\\x19I\\xfc\\x1e|\\xbax\\xfe?\\xcdY\\xb26\\xaa\\xb7\\xed\\x06\\xc1\\xbe\\xef\\xbb\\r[^\\'uD\\x15\\x08R@\\x04\\xf1\\x8a\\xeb\\xf1\\x16e\\xd3\\xfeO?Jj\\xb5O]x\\x1e\\x8d\\xb0\\xb1\\xadgN\\x8b|\\';c\\xfbf\\x9d\\n\\x9e#\\xd5R\\x1dCa\\x14iluilzp\\xa8m\\x98\\xf6\\xe4\\'gm\\x0f\\xe8\\xde\\xb5\\xefB\\xc55NX\\xed\\x82\\x8f\\xcd\\x923\\x1a\\xcf\\xba\\xa6i\\xd6\\xfd\\x17\\x931\\x98\\x82|\\xea\\xf2M\\xa9^%O\\x03\\xcbJ|D=\\xf3\\xc234lhk\\x0b@F\\xfaT\\x90\\xa4o\\xa4\\x9f\\xac\\x9c\\xe0\\xfb\\x8et\\xf3\\x16\\xc1\\'x\\x13\\x12\\x06d\\x81\\xa0\\xac\\xf3|\\x94\\x1cT\\x95\\xdaA\\x88\\xde\\x1a\\xd3G\\x83\\xf6\\xa6-\\x1b9\\x1c\\x93\\xf0\\xaapN\\x95\\x1c\\xf2\\x11D\\xa8\\xe5\\xbbn\\\\]2\\xc3H+u\\xd5\\x86\\xd2\\x90\\t$\\xa8\\x80<\\xf5\\xa7\\xcf\\x10x7$\\x0c\\xc4\\x11#\\xd6* \\xb0S\\xbaH\\x8d\\xe9\\x83\\xce*\\xf2\\x05\\'t\\x90#\\xa4iV\\x90\\xde>3\\x9f\\xd5\\x11>B\\x98\\x96]\\x9aI\\xed\\x1bd\\x84\\x18\\xfc/k\\xfe\\xf0Vu\\xeee\\xe1\\xdft\\x85>\\xd3\\x9f\\xc4\\xd8\\x1c\\xdd\\x1f\\xdd5\\x18\\xe5^\\xd4V\\x95m\\xc6\\xd3\\xef\\r\\xef\\xc7m\\xd2S$\\x03\\xf4h\\xe2+\\xdb\\xf0\\xf2\\xf6\\xcd8\\xf5\\xf5\\xdd7\\xf4n\\xf6\\x9d\\xd8XJS\\x00\\x10\\x07\\x88\\xe5^<\\xaf/\\xa5\\x96\\xd5\\x18\\r\\xd6\\xee\\xd2\\xdd\\x16\\xed\\x1e\\xee\\x9d\\xb0C\\x8a|\\xa9;\\x88Puc\\xbb\\xdd\\xf4\\xa7\\x8c\\xe9]\\xfa\\xb7\\xfd\\xace\\xf7\\xfc\\x9ek\\xf7\\xea\\xc5\\xbcJ\\xe1\\xcd\\xa3q\\x87,\\xca-\\x93bT\\x9b\\xb0\\xa4\\xee\\xa9D\\xaaQ\\xbb3 \\x00d\\xe5\\\\%\\xfd\\x8f\\xe2\\xe9\\x8c\\xe4\\xdd\\xbbJN\\x1f\\xf3\\xb41\\x87\\xbfl\\xdb{\\xcay\\xbb\\x16\\xd7\\xa4N\\x8f\\xf0\\xe9^\\x9f7_\\xaf\\xec\\xe5\\x9d\\x9eM\\xec\\xc2\\x9a{\\xb4\\x06\\x9ed$\\x05X\\xbe\\xb1\\xba\\xd9lAR\\x06I\\x93\\x1cx\\xd6\\xfa\\xd2\\xce\\x9c\\x95\\xc7\\x1d^cF\\xaf\\x1e\\xdba`\\xd0\\xe8\\xb7\\xc1\\xff\\x00\"\\xbe[\\xdfe:\\x16\\xac\\x125\\xca\\xad\"\\xa3J\\x80\\x08\\xa9\\x12\\x97\\x12\\xb5\\xa9 \\xf8\\x86\\xa24\\xabJ\\x94t\\xca\\x9d2\\xce\\xbbjHV\\xcd\\xb5\\xd2\\xf1\\x83\\xee]g(\\xa3\\x12\\xb1\\xfc\\x8acI>\\xac\\xaa\\x88\\xea\\'\\xf1\\x8dxe=ED\\xda\\xe4\\x12j\\x14\\x07\\x08\\x9fUP\\xd4\\x9b\\xe3\\xbc\\x8c8\\x12LZ%1\\x1ax\\xd7\\x97\\xbe\\xb5\\x96\\xf8\\xb0Dp\\xa2\\x01\\x00\\x989\\x11:\\x8a\\xcf\\xaa\\xbbB\\xc4\\x8f\\xe2\\x99\\xf3\\x1a\\xf1\\xa9S\\xf6\\x9f\\x9a\"r0\\x9f\\x85QO\\x07x\\xc1\\xd7\\xae\\xb4pS\\xf0%\\xa5\\x8d\\xa0\\xc2\\x9cwx!\\xab\\xd6T\\xad\\xdc\\xcc\\x07\\x12L{+X\\xd92\\x16n /\\xf2\\xab\\x9e./?\\xf4\\x8d\\x16\\xa1(\\x9c\\x81\\xd0O\\x95H>\\xa8\\xe4:\\xd2\\x8a\\xb8\\xf4\\xd6\\x06\\x90>\\x14D\\xb4\\xec\\xc4\\x7f\\xd6F\\xc9\\x0f\\xfc^\\xdb\\xfb\\xe2\\x8a\\xa3\\xbe\\xa9Jm\\xa8\\xfc\\xda\\xdc\\x7f+\\xff\\x00\\t\\xa0\\xc7\\'v\\x90\\xb0v\\xf7i\\xb7\\xc1(8\\xa3)\\x80\\xa83\\xb8\\x8a\\xf7t7\\xdb\\x1c\\xba\\xb3v\\x7f\\x06\\xfa\\xd1N\\xfa\\x86\\xe2\\xbd/\\xd2\\x1fuxr\\xf2\\xfaYE(u\\xb5\\xed>3f.\\xd2\\xd5\\xda\\xac\\xd9q)\\n\\x05\\xc4 \\xa9^ 9O\\x1e\\xb5\\xe9\\xb2^\\x96\\x0f/7:\\x92\\x8b\\xebeb\\x9f7n\\xed\\xa5]\\xb1m\\xbc\\xea\\x03\\x80,\\x080\\xa5\\x01\\x9af\\t\\x07\\xee\\xaez\\xed\\x9f\\xc5\\xd7\\x19\\xce\\xaa\\xc2\\xe1\\x9b\\x97\\xf0k\\xa7\\x8d\\xcd\\xfa\\xd8\\xee\\t$_\\xb4\\xebj\\x112a\\x00\\x9fQ\\xce\\xbbcd\\xca\\x7f\\x87\\x0c\\xf5\\xe1S\\xd9\\xd1[\\xbb\\\\\\x95\\xa9R\\x11\\x84\\x93\\xd4\\x15:>\\xea\\xed\\xf1~5\\xf5r\\xe9\\xfd\\xd8\\xd4\\'\\xfcEx\\x1bai\\x9a[[\\xe0\\xc7\\xe8\\\\\\xe8\\xaf\\xb2\\xa1\\xb5\\x88\\xa5l\\xa1S#\\x80z\\xd1\\xa48\\xe9N\\x90\\x88\\xce\\x9d\\n\\xcf\\xfbe\\x94\\xec\\xb9Q\\x82\\x13unL\\xfe\\xd2\\x87\\xdbY\\xcb\\x88\\xb1\\xbc\\xb0\\xcb,\\x9a\\x8c\\xf2W\\xba\\x86\\xa9\\xe6\\xbf\\xcf\\xe7\\x9c\\x01\\xef\\xaa\\xaf\\x06\\xd6\\x04\\xab)3:Vt\\x06\\xb07\\xa1#/(\\x9am \\xe2\\x8a\\xb7\\x02\\xb4BwG\\x13\\x12L{\\xea\\xbe\\xcb\\x82s\\x833\\x9fH\\xa8!\\xe23\\xf3\\\\\\xe4x\\x85Kg\\xec\\xf2\\xb4n \\x08N\\x9eUD\\x92\\x00\\xee\\xd4b\\x15\\xa8\\xfd\\xd4B$\\xb9\\xdd\\xdc%\\xd4H)^\\xf0\\x9f9\\xa7\\x98\\r\\xab5\\x1f2j4\\x15\\xa9\\xd2\\xa43\\x1b\\xb9\\xce\\xef\\x18\\xd6\\xaf\\xaa\\x0b\\x9fMyh5\\x8e\\x95EV\\xbd\\x96\\x82{K\\xd9\\x11;\\xa4\\xe2\\xb6\\xf9\\x8dr?\\xba\\xb3y\\x11\\xde\\xfb\\xa7\\xf4\\xd5\\xec\\x1fu\\x1a\\xbe\\xe7jM\\xa6\\x05-\\xdb\\x02\\xa2\\xa0Vu\\x8eU\\xa9\\x13\\x95\\xf6\\xbd\\x95b=\\xa1\\xed5\\xb3e\\x01g\\x13*I3\\xaa\\x12\\x9c\\xbdu\\xed\\xe8\\xddc\\x07\\xcb\\xbdL\\xe62\\xbdc\\x9bg\\x8eX\\xa1\\xb0\\xfb8r\\x96\\xa0\\xa8**\\x1b\\xd0F~\\xf1Y\\xcf\\xa5\\x86\\xb6\\xf5\\xdc\\xb3\\x97\\x99\\x1es\\x14\\xdbK\\xf7\\xaf\\x18\\xben\\xe16n\\xbe\\xce\\xeb\\xed\\xa4 \\x9f\\x0c\\x81\\xbaJgt+=\\xd3\\xed\\xae\\xd8\\xc9q\\x92G\\x1c\\xe5\\xc7-\\xd7\\xa5\\xd8\\xccb\\xe2\\xf3\\x00\\xb6\\xbey\\xf4\\xdd_6\\xf3\\x8c\\xdc\\xad\\x92\\x96TR\\x7f\\'\\n\\x89\\x03<\\xb5\\x8fur\\xcf,d\\xe5\\xd7\\xa7\\x8eW\\x97\\xa2\\xbc\\xc6\\x1bM\\xba\\xdbuW\\x0e\\\\8\\xca\\xc0pc\\xbd\\xe0\\x1a\\x8f\\x1b[\\xa0\\x05sL\\x0e5\\xae\\x9c\\x97)\\xfe\\x1cz\\xb2\\xccn\\xbd\\x12{(p\\xbd\\xb5\\xd7\\x86=\\x0c!\\x9fV\\xf3\\xa7/ut\\xf8\\xde?\\x9b\\xcb\\xd1\\xbb\\xc65\\x9a\\xf0;0\\xa4\\xd2\\xd2\\xdf\\x06#\\xbbs\\xa2\\x87\\xc2\\x9d\\n\\xb3\\x11P\\x18\"\\xa4T\\xf4\\xa8\\x01UH\\x9d\\xec\\xf5\\xad2\\xf0=\\xb3\\xe7\\xb1\\xd7\\x86G\\x81\\xdbs\\xfd\\xa7\\xef\\xacg\\xe1\\xa9\\xe5\\x85\\xda\\x1f\\x04N{\\xda\\x1f*\\xe7\\x1a8\\x82\\xad\\xe7\\xc6{\\xa5:z\\xc56\\xaf$)BH\\'\\x85*\\x86\\xf0\\x82g/*\\xaf {\\xdc\\xf9i\\xad\\x08d\\x8c\\xf3\\xd0\\xcd[(x\\x9a\\xbf\\x151\\'\\xc4=u}\\x05;fb\\xd9\\x04\\x9e\\t\\xf8U=\\x8aF\\xf0\\xdd9\\xcc{\\xe8\\xde\\x86\\x89\\x04s\\xa6\\xf2\\xa0\\x94x}\\x91T\\xb1z\\x88\\xaa\\x08\\x93\\x97\\x9d[Wb\\xde)\\xf1\\xa4\\xc2\\x92d\\x11\\xaf\\x9dV\\xa1\\xdd\\xe4\\xeb\\xd3\\xae|5\\xca\\xa9Q\\xdd\\x88\\xb9\\xbb\\xb2\\xdb,\\x02\\xeb\\x0c\\xb6n\\xee\\xfd\\x9b\\xf6\\x96\\xc3\\x0b^\\xe2]X9 \\xab\\x84\\xf3\\xe1VwR\\xd3\\x8c\\xdd\\xd3\\xa9\\xd3\\xb6\\xfd\\xa8?\\xf9\\x1d\\x84\\xc2\\x19\\xcf.\\xfb\\x17\\x06=\\x80W\\x9aug\\xa7\\xf6\\xff\\x00\\x97_\\x95V\\xb8\\x1e/\\xb5\\x98\\xa2_N\\xd9`\\xf8v\\x1a\\xa6\\x94\\x95[\\x1b+\\x9e\\xf8:\\x08;\\xf3\\xc8\\x8f\\x0f\\xb6\\xba\\xf4\\xf3\\xee\\xe5\\x8c\\xb1\\xb8\\xb9\\xeb\\x1f\\xbd6\\xdd\\xa6\\xed#\\xfb\\xe1)\\x18\\x9b\\xd2T\\xa2\\x90 \\x013\\x07\\x94W\\xd0\\xe8\\xf8\\x8c\\xe3\\xd4\\xf9y\\xf7X\\xb2{\\x17j\\xfd\\x8b{lIhS)\\xbaC\\xcau\\x00,we+\\x98W\\x0c\\xcadyWK\\x8f\\xb3\\xd5\\xf3\\xb0\\xba\\xb9\"\\x9d\\xa4N\\x1c\\xa6Wq\\x84\\xdab\\xc9,\\xa1\\xa1n\\xeaR\\x12\\xd1\\x04\\x9d\\xed\\x15\\'\\x87\\xfc\\xe9\\xb7^\\x8e\\x1d\\\\\\xe7\\xa2\\xe5\\x8d\\xab\\xb4~\\xde\\xc4\\xaf\\r\\x16M\\x05)\\xc5\\xdb4\\xd0(A& xzN\\x9ck\\x19\\xe1\\x97Vp\\xeb\\xd3\\xebc\\x86<\\xd5\\x93X\\x8e\\xcf\\xbc\\xeb\\x8f\\xab\\x13\\xc6\\x1aS\\x8bR\\x96\\r\\x83.\\x01&bw\\x82\\x88\\xcf#\\xac\\n\\xeb>\\x1b\\xa9l\\xe3\\xc7\\xe2\\xf3\\xf5>+\\x0ce\\x96\\xbdwc*\\xef6\\xab\\x1eP\\x1e\\x16\\xf0\\xdb4\\x03\\xa4\\xca\\x96f8yV>3\\xd1\\xe7\\xe8\\xde8k\\xb3^\\x1d;\\xb0\\x94\\xa8\\x1e\\xb5\\xa8\\xd2\\xdb\\x08\\\\%\\xd9J\\x93\\xa6\\xb9T\\x16\\x1d\\xf0\\x8eU\\x00/\\xf5\\xabkD\\xaa\\xe7\\\\\\xc4\\xd1\\xb5\\xa2\\x17r\\x07\\x1a\\xb6\\xb4o\\xe7C\\x99\\xa2\\xe44\\xf2}\\xab\\x16\\xde\\xec\\xff\\x00\\x16YL\\xad%\\x82\\x0f@\\xeah\\xb6S\\xad0\\x8bc\\x00\\x8c\\xe6h\\xc6\\x9aq\\xb3\\nz#\\xd0\\xfbE\\x17\\xc8\\x86\\xd4|G<\\xfc\\xea\\x8a\\x88j\\x08\\xa5$\\xdd\\xa1\\x96\\xd8\\xc3\\x94\\xd8;\\xce0\\xa5;\\x9f\\xd7\\xefT2\\xff\\x00D\\'*\\xd5\\xf1\\xb0i\\x05 +x\\x13#(1\\x07\\x81\\xac4\\x87\\x88\\x18\\xb6#\\xa8\\xf6\\xd3 \\xd9\\xdbS\\xf8\\xa3ypMFrvs\\xf7\\xe5\\x95\\x1e\\x8a\\xa5\\xe0\\xe8a\\xecZ\\xd5\\xab\\xc2Sl\\xb5\\x90\\xe1\\x07?D\\xc7\\xbe+RM\\x8a\\x80\\x82\\xa2\\xca\\n\\x87\\x8e\\x04\\xe7\\xc4\\x8a\\xb5\\xca\\xa3t\\xa6\\x11\\xba\\nT\\x01\\xde\\x93\\xaeukka$7\\x108\\xf0\\xa2\\xc5\\xa2\\xaf\\x8f\\xd3\\xbeN\\xb9\\xfc*\\x90\\xd5\\x97e\\xe8\\xdf\\xed\\'d\\x80\\xcb\\xf8M\\x9fl\\x9a\\xcfS\\xee\\xd8\\xa5\\xe5\\xd9AKB\\xa6A3\\xca\\xbc\\x9aw\\xee%\\xe5\\xa9e;\\xc27f\\rw\\xe8O,u.\\xde\\x0f\\x16\\xec\\xc3d\\xef\\x7f\\x0ec\\x17\\x967n^)/^,\\x8b\\xf7P\\x85/t\\xa8\\xf8RD\\x02F\\x95\\xe9\\xdd\\x8eLQ\\x86\\xf0u\\xb2\\xd9\\xff\\x00\\'\\xad\\xc6\\xf2A\\x81\\x88\\xdd\\xc6y\\xff\\x00\\x19^\\x8e\\xddz\\xb3\\x0e\\x06p\\x922\\xc1\\x10\\x913\\x03\\x11\\xba\\xd7\\xfau\\xa9\\x8d\\xf7\\x14\\xea-\\xf0\\xd8\\xcb\\rRyF#r?\\xe3\\xabW\\xdc\\xec\\xb6l0\\xe7\\xee\\x18A\\xb3z\\x1cq\\r\\x9f\\xe1\\x07\\xe6\\n\\x80\\xfd.\\xb4\\xef)\\xea\\xcd\\xc6{:Cb6/\\x08\\xd9\\x17\\xf1\\x05`\\xdf=\\xde\\xba\\xddK\\xa6\\xe6\\xe5OHA1\\x13\\xa6\\xa6\\xbc\\xf9\\xe7s\\xd6\\xda\\xc6I\\xe1\\xea\\x8a\\x88\\'/ur\\xdbzc\\xc1\\xf8\\x19\\x00\\x07Jv\\xdd\\xc4\\x0b\\xe6u\\xf5\\xd1\\xdc\\xb4IyY\\xe6G\\xae\\xb3mZ$\\xbac,\\xea\\xda\\xd0o\\xa8\\xf1:VV\\x8d\\xadfsW\\xbe\\x98\\x01\\n\\x07\\x8c\\xf9gV\\xd6\\x94\\xbd\\xa36\\xa5v\\x7f\\x8d\\xf8W\\x93HVi\\xe4\\xe2(\\xee\\x9e\\xe7\\xb6\\xb0\\x96~\\xb4i\\'\\x8dk\\x18\\xcd)\\x1e\\x9b\\xdf\\xcd\\xeb\\xa7*}Y!|j\\xd1)^\\x19\\xcc\\x19\\x19T\\x81\\xd5\\x0e\\xe9\\x88X$ \\x829x\\x8d6\\x0bI\\x07\\x8f\\t\\xcaM\\x07h\\x98\\x81\\xfcU]\\x08\\x9a|\\x03\\xd6\\x84|\\xd1\\xa1\\x90\\x84\\xa6\\x84~>\\x8c\\xaas\\x14\\x93\\x96\\xa6/Z\\xdfX@\\xde\\xcdZ\\xc6_\\xf2\\xaaNB2d\\x04\\x8dM\\x04\\x14s%1O\\x96@\\xc9\\xd0\\x13 \\xe5\\x15BU\\xe9=\\xfd\\xc6|LU\\xa4\\xbe\\xecm\\xbe\\xf3\\xb5m\\x90O\\xfe \\x95{\\x10\\xb3\\xf6U\\xad\\x8b\\xc3\\xb7\\x1c\\xb0\\x04i\\\\\\xefN52\\xaa\\xacJ\\xdc0\\xe3`}`M8c\\xda\\xad\\xda\\xb3\\x10;\\xbb3\\x8f*b,n\\x0f\\xf6J\\xae\\xbe\\xb1\\x98\\xe6\\xcc\\x12\\xdd\\x87\\xec\\xd4\\x1eI+J[\\xdd\\x87wu\\x19\\xf9\\xf9\\xd7\\xab\\xd7aj\\x8c6\\xcc\\xa8n\\x87Nd~^:Fb\\'\\xe3V\\xc5\\x89\\x0c\\xe1v{\\xb9\\xa9y\\xaa\\x01\\xef\\x80\\xfb3\\xf7Um1\\x16\\xd1\\x08\\x18\\xfd\\x9bM!hH\\xbbi;\\xab^\\xf9\\x90\\xb0\\x0ep2\\xe5\\x95>\\x9bYxu\\x1b\\x07\\xe9\\x1d\\xfd\\xa3^JAn\\x10\\xb5\\x0c\\xf5\\xaew\\xcbl\\x91\\x16O\\x91\\x9a\\x12?ic\\xec\\xae\\x7f7\\x18\\xef\\xf2\\xe9J\\xb4(\\xfc\\xab\\xed7\\xd0\\xcf\\xdb\\x14|\\xd9\\xe8\\xbe]\\xf5 \\x8bD\\t]\\xe0\\x8f\\xd5\\x03\\xf7\\xd1\\xdf\\x95\\xf1\\x17d\\xf7)\\xb3j\\xe6L\\xa6\\xe9\\xff\\x00\\xd9B\\xa0\\xfb\\x85\\x16\\xe6\\xb5\\x8aJm\\x1dX\\x1d\\xd6\\x16\\xfa\\xa7\\x8b\\x90\\x9f\\x89\\xac\\xf7_Zu=!\\xd4a\\x97\\xea#v\\xd6\\xd5\\x9f\\xdarO\\xb8Ql\\xf7:\\xbe\\xc9Ma\\x17g\\xf2\\xb7\\x8c\\xb7\\xd1\\xb6g\\xfb\\xc6\\x8e\\x16\\xaa\\x9b\\xb4\\x8c\\x1d)\\xec\\xe7iT\\xab\\xc7\\xdcZ,\\x94\\xb0\\x9f\\x08I\\x85$\\xe6\\x00\\xd2\\x9cn\\xacg)\\xc3\\x99\\xc4\\x01#BN\\xa7\\xadzq\\xe5\\xc2\\x89\\x07\\xc6\\xe6R7\\x08\\xadl\\x08\\xf5\\x19\\xd3\\xf8\\x1f#\\xd7Y\\xf6T\\xc8\\x109eR\\x18\\x038\\xd3\\x8d\\x17\\xcaE\\xc4r\\xb5TpP\\xa5\\x15e\\x1f5o,\\xa11\\xec\\xa2/\\xa2L\\xe5\\x11\\x97SQ\\xf2D\\t\\xa7` r\\xfd\\xf4O\\x08\\x85F\\xf7\\x19\\xebL\\x82\\x8cBbI\\x89\\x9f\\x0e\\xb5ZGx~\\x96\\xe29\\x9d4\\xaar\\x9e\\xaf\\xb0\\x94\\x07;_\\xd9@s\\xfcij\\xf60\\xe9\\xad\\xe3\\xf9VrwF\\xe8 H\\xae{:y\\xfd\\xa6\\x10\\xfd\\xb7-\\xd5|E%\\xe6\\xb1\\xb5nln\\xd1\\xaf\\x95\\x85\\xc6\\x7f\\xea\\xcdjy\\x8a9\\xebgU\\xdd\\xb0\\xb3\\xbf\\xbb\\x05\\x19\\xc8\\xe5\\xc7\\xa5zY[4\\xa0\\x03\\x89\\x07Rw\\xd4\\xa5e\\'\\x86\\x9a\\x0e\\x13?z\\x13\\xdaR\\xd4RJ\\x8f\\x91Y\\xe3\\xaf\\x08\\x8f\\xbe\\xaab\\x05\\xa1K\\xfbg\\x87%\\tX\\xfcu\\x94*L\\xe6\\x16\\x04\\x8c\\xa6 \\n\\xbcb\\xb2t\\xbd\\xbc\\xef8`\\xc1Q\\xaf5(\\xcf\\xba\\xa0\\xf3\\x80\\x0c\\xb7\\x8f\\xc6\\xbc\\xf7\\xcbo\\x02\\xde\\x03k\\xa3\\xf7W\\x8fq2\\xf4|+\\xcf\\xf3=\\xa3\\xdb\\xda\\x92\\xce\\r\\x853\\x98\\xb3mG\\x9a\\xc9W\\xc6\\xae\\xfbGdMm\\xbbV\\x00\\xee\\xad\\xdaA\\xe8\\x80\\r\\x1d\\xd5v\\xc8{\\xbe=c\\xce\\x84\"\\xe9\\x8f\\xabR#|\\x93\\x9a\\xaa\\xd8)\\'\\xa8\\xa9){AN\\xff\\x00g\\xbbP\\x9e?\\x83^>\\xc1?ek\\x1f1\\x9c\\xbc9D\\xac\\xa9\\x01G<\\xcf\\x1e\\xb5\\xe9\\xc3\\x87\\x9f/\\x02A\\xfaeLz\\x07\\xe1Zd\\xd2\\x8c\\x9e\\x14\\x82\\x81\\xd7:\\xbc\\xad\\x14ri\\xb5\\xc8\\xf1\\x95z\\xa2\\xaf\\x08h\\xf1\\x93\\x04hNf&\\xa8t\\x89|\\x7f\\x16W\\x98>\\xfaQv\\xaa?4k \\x08JD\\x8e:\\xd5 =\\xbcwL\\xf2\\xca\\x88\\xa5\\xf7;f\\xca\\xae\\xae\\xdba\\nJT\\xb2@*0&\\t\\xf7\\xc4U\\x11\\x90\\xbd\\xe4\\x82s\\x914\\x99A\\xc0RS\\xc9BGZ\\xa0\\x14\\x82\\x019\\x1a\\xaa\\xa1t|O\\xf0\\x82f\\xa8\\x1e\\xd7\\xe4\\xfc\\x90\\xae\\xd96XF\\x8e\\xbe|\\xbf\\x17v\\xbac\\xeb\\xf81k\\xb9\\x06\\x82\\xb9:<\\xf6\\xd3\\xfeqo\\xd1\\n\\xcb\\xd6)\\x89\\xe21\\x96V\\xce\\xc3mC\\xaa^_\\x83\\xae\\x13\\x00\\xeb\\xe19\\x9e\\xb5\\xb9\\xf7\\xa0\\x9c\\xb9\\xf6\\xca\\xf1\\xcbDCA\\x19\\xc1\\xf1L\\x822\\xe0Ezt\\xcab1{\\x8c\\xa46`@\\x95/!\\xfd*t\\x8f+\\x13y\\xd6\\x16\\xcb\\xcd[\\xb8\\xda\\x8e\\x8b\\x0b yx\\xb8kU\\x86%\\xec\\x82\\'j\\xf04\\x81\\x97\\xcfZ\\xcb\\xd7\\xfb\\xaa\\xca\\xf1Y\\xae\\x89\\xc3,n\\xff\\x00\\x0c\\xbfz\\xf5\\xd9[\\x04\\x14\\xb6\\xd2d\\x089\\xc2\\x86\\x84\\x8e\\x04W\\x9f-kR5\\xba]\\xcd\\xe2\\x93p\\xea~ap\\xb8Y\\x1b\\xc0\\x089\\xeb^{y\\xf0\\xe9\\xaf\\xab\\xca!zG\\x9dx\\x1fD\\xb0\\xb9\\xe7\\x14\\xb2p\\x13\\xc6\\x9d\\xa2\\xd2\\xad:\\xd0\\x00\\xa8s\\xe1K\"\\x06\\xa4u\\':B\\xbbl\\x13\\xdel^\\xd1#\\x9e\\x1bs\\xfe\\xec\\x9aq\\xf2\\xcd\\xf0\\xe4V\\xd4\\x0bh1\\xc2k\\xd7\\x8cy\\xe9H?Ly\\xee\\x9c\\xa3\\xa5?FM\\xaf9\\xe2#\\xd5J\\x19\\x94\\xeb\\xec\\xab\\\\\\xaf\\x07\\x1c\\xfc\\xd6\\xdf\\xcds\\xc6s\\x14\\xde $\\x1dd\\xfb\\xa8\\xdaE\\xbe\\x9f\\x9b\\x1f1I.\\xd0\\x8f\\x9a\\xb7\\x07@(\\xf16\\x0f\\x00@&<:LR\\x92,\\x087\\xad\\x05IH*\\x91\\xc7\\xd15x\\xa9\\x15\\xb2J\\x11<@\\xd3\\xd5V\\x88\\x13\\x9c\\xe9\\xd6\\xaf\\x02\\x8f{0jC\\xbc>\\'\\xc8\\'U@\\x8e\\xb5b\\xaf/w\\xf2xL\\xf6\\xcf\\xb3\\x02\\x08\\x83r|\\xa2\\xd9\\xda\\xdc\\xf1\\x7f\\x0f\\xce1\\x97\\x98\\xed\\xe1\\xa0\\xaem\\xbc\\xe6\\xd3\\x9f\\xc6\\x99\\x9e\\r\\x9f\\x88\\xa6xJ!io\\x89a\\x17\\xd8u\\xd8Y\\xb6\\xbbmL\\xbb\\xdd\\xabu[\\xaa\\x10`\\xf0=k^\\xbb\\x0f8\\x9e\\xca6N2k\\x14\\x1f\\xed\\xea\\xfb\\xab\\xa7\\xcc\\xc8\\x16\\x9e\\xca6P\\x1fC\\x14\\xff\\x00\\xd7+\\xee\\xa7\\xe6\\xe45\\x0b\\x1d\\x94\\xec\\xacd\\x9cS\\xff\\x00\\\\~\\xea\\xbenKG\\xec{:\\xd9\\xdc3\\x10\\xb5\\xbd\\xb5\\x18\\x80\\xb8\\xb6p:\\xdfytT\\x99\\x1aH\\x8c\\xc5]\\xf9^\\x15\\x9b{k1\\xf4G\\xa9\\xaeu\\xa8\\xbbJ\\x06\\xe8\\xcb\\x85gi\\x8f!\\xd3\\x1aI\\x8eu\\xf3\\x1fT\\xf2\\x14O\\x08\\xcf\\xce\\x96N\\xa4\\xf3\\xff\\x00\\x9dP\\x1cL\\xfa\\xfa\\xd2\\x039\\xe3\\x95,\\xd2\\xd2\\t\\xe3P:\\x81RG\\xc7\\xd0\\x17\\xb3\\x98\\xca5\\xde\\xb0\\xb9\\x11\\xfe\\xa9T\\xc6o\\x87\\x1c\\xda\\x99\\xb6d\\xe6| \\xfb\\xab\\xd5\\x8b\\xcf\\x97\\x83\\xa8\\xfc\\xb8\\xf2\\xe7\\xc6\\rol\\x90z\\x0fW*=P\\xcc\\x91\\x07\\x80\\xf3\\xa7^\\xc0FH\\x03P3\\x03\\xa9\\xa5\\x0c\\x03\\x95f\\xa4[\\xfc\\xad\\x94I\\xfd\\x13\\xafZ\\xd7\\xa2.\\xcf\\xf3dG\\x08\\xd7*\\xa5\\xf7I\\x00\\xc2 \\xf1\\xcfZ$@\\x85)\\x0b\\n\\x06\\x144>t\\xad\\x91\\xbb\\xa0\\x8c\\xaa\\xd9\\xa4\\xa8\\x02\\xa1#2\"\\xaf,\\x96=$\\x83\\xc4\\xe7\\xf7\\xd1\\xe8v+\\xb3\\xe3\\x7f\\xcc\\xf1\\xebN4=\\xff\\x00\\xc9\\xc4Ol\\xfb7\\x91\\xc9\\x17_\\xee\\x17[\\x97\\x8b\\xf8~q\\x8b\\xe6~\\xbd+\\xb7k\\x9b\\xa3\\xcdmB\\xa2\\xf1\\xb1\\xfc\\x91\\xf8\\xd3<%&\\x12\\x95\\xa9\\xb5\\x11>\\x95j\\xd1\"\\xd1\\x08\\\\jj\\xda\\xd1\\xc0\\x95s\\xa7`\\xb0\\x17\\x15l\\x9bu*\\xdd4\\xca\\x12\\xac\\xc7\\xd0\\n)\\x8b\\xe1\\x90\\xd2\\xb9\\xec\\xb14\\xef2\\xf2\\xdas\\xd2mD\\x10+\\xe7>\\xa6\\xd2\\xdbQT\\x13N\\x99\\xa7\\xd04\\xe0|\\xa9f\\x9d\\x19\\r:\\xd3 \\xb4\\x13\\x9f\\xddN\\x81`\\x1a\\x99:\\x94\\xe9\\x9c\\xcd(.\\xdb\\xef0\\xdb\\xd4~\\x9d\\xb3\\xc9\\xf6\\xb6\\xaaY\\xbe\\x1cY`\\xa9\\xb2c?\\xf3c\\xe0+\\xd1#\\xcfo\\t\\r\\x9f\\xc6\\x10\\x0f\\x1a\\xe9|\\x03Z\\x0c\\xf4\\x8c\\xe8\\xf26\\x04\\x8c\\xc19\\xf3\\xe5W\\x94^{\\xb3\\xe2\\x83\\x94\\xd5H\\xb7\\xbc\\xff\\x00}^\\xa1\\x1a\\xfd_\\x8a/\\xd4\\x08\\xe4&\\x95\\xb2\\xec\\x8c\\xdb#\\xc8dk:\\xd5G\\xce\\\\\\xa3\\xa1\\xa6^\\x16\\x85;\\xca\\x00\\x0c\\xceQZB*\\x94\\xeac\\xca\\xb3\"\\x12\\xcec\\x9fJ\\xd4\\xda\\xfa\\x802A\\x1a\\x83#\\x95\\x10\\t\\xf5\\x18p\\xe6I\\xcf\\xdfT6\\xb4O\\x93`\\xde\\xed\\x9f\\x019\\xf8Y\\xba?\\xd9\\x11\\xf6\\xd6\\xe6\\xb5\\x7f\\x0f\\xce9\\xdf3\\xf5\\xe9]\\xb7\\xc2\\xb9\\xba<\\xc6\\xd4O\\xcf\\x91\\xfc\\xc8\\xf8\\x9a\\xd4H\\x98\"?\\x17\\x9f\\xd65%\\xa2R*\\x05\\x84\\nQ[\\xb9T\\x8c\\\\\\x0f\\x01\\xa6\\nr\\xcc}\\x12\\x07Z\\xa9\\x9e\\x17\\x95\\xc8\\xb1g\\xd8ZT\\x80\\xff\\x00\\xe7\\r\\xfd\\x0b\\xa2s\\xdeN@\\xfa\\xc4{+\\xe6\\xf42\\xee\\xc7\\x9f3\\x87\\xd6\\xebMeu\\xe2\\xf2u\\x91\\x98\\x8dG\\n\\xed\\xa7\\x1bR\\x922\\xe7N\\x86\\xce\\x81V\\x99\\xd8\\xd28\\xd3A\\xc1\\xd7\\x8f\\x01F\\x81\\xc1\\xc6g\\x9d(\\xeaF\\xf2\\x1cDzHX\\xf6\\xa4\\x8a\\x85q%\\x8aG\\xcd\\xd0\\x93\\xc1\\x02\\xbd8\\xf1^{\\xe1)\\xb2>p\\xdeq\\n\\xcf\\x85j\\x83$\\x88\\xcb@9\\xd1\\x11I\\xfd\\xd9R!\\xc5A\\xb5Jr\\x9d\\xf2g\\xd4)\\xb1HLBA\\xd4\\x13\\x11E(x\\x84\\x1bE\\x08\\x031\\x9c\\xeb\\x9d#\\x8d\\x9c\\xb1\\xca\\xd5\\x1c\\xc0\\x02\\x83O\\xa8p\\x1e\\xea\\xa0.\\xc8\\xee\\xde[\\x93\\x00\\x05\\x8e\\x15\\xad\\x8b\\x0c\\x8e\\x07-5\\xa0\\x90\\xa89\\x88\\x9a\\xb6\\x07\\xd7\\\\\\xf8\\xd5\\xf4\"{\\xf2N\\x18\\x98\\x19\\x88\\xd0MZ[h\\x1f\\'\\x17C]\\xb2\\xec\\xe2\\xb8\\x14\\\\\\xa3\\xda\\xd2\\x87\\xc6+S\\xd61}+\\xb8\\x07\\xb2\\xb0\\xdb\\xca\\xedB\\xbf\\x84\\x80\\xcf&S\\xf1UjxF\\xf0|\\xad\\xc7\\x9dAd\\x9a\\x91\\xc0iEp\\xa9#\\xdc\\xfa\\x11L\\x14\\xed\\x98\\x90\\xd0\\xea*\\xa9u\\x9dsi\\x90\\xb2\\xb5_2\\x97\\x94\\x87\\x13r\\xb6G|\\x95\\xa7t\\x85\\xa0@V|\\xd2+\\xe4\\xf4{\\xa6[\\xd7\\x17\\xfb\\xff\\x00\\xcb\\xe9w\\xcc\\xfaS~a\\r\\xe84\\xafc\\x9aBO\\xaa\\x8d\\nZU4\\x82\\x90|\\xa8G\\x04E@\\xa4\\xfaT\\x84\\x8b\\\\\\xdfle\\x99\\x8fm\\n\\xb8\\xa1\\xa4\\x84\\xadBguJI\\xf5(\\x8a\\xf4J\\xf3\\xd8\\x92$\\'^\\x91]#&\\x9c\\x1e5\\x92\\x0c\\xceTD,\\xf7\\x93\\xaec\\xce\\x842e\\x9d\\xce\\x12U>\\xaa}U\\xa1\\xf5\"@=\\x06u_%\\x0f\\x10\\x81j\\xe1\\xc8i\\xf1\\xa5z\\x8e\\xc4~&\\x8f!\\xf6\\xd1\\xe5\\x9di&s&DNuB6T\\x10\\xf3j)\\xde\\t \\x94\\xf3\\xe9Q\\xf0l\\x88\\xe3\\xeb4\\xca\\xa1*:\\x022\\xe5F\\xb644\\x89 (\\xe9\\x1a\\xd3xI\\xa8e\\xb7p\\x9b\\xb5\\x95(:\\xda\\xa5;\\xaa\\x83\\xbb<G\\x11T\\xab[zN\\xc1\\xd4Q\\xda~\\x10\\xe2`\\xa9\\xb6.\\x16\\x0f\"\\x1b1L\\xbc\\xb1\\x9b\\xb9\\x98t<\\xd2\\x1cI\\x1b\\xabHP\\x1ebji\\xe6v\\x94\\xce&\\xbf\\xd5i#\\xe2j#\\xc2?6MIb\\x9a\\x81b\\x94>\\x15$k\\x93\\xe1\\xa6\\n\\x93e\\xab>b\\x8aW\\x15\\x82\\xc2\\xf0\\r\\x8b\\xb7\\xd9[V.\\x9a\\xbf\\xba\\xbf\\xbd\\xc5Z\\xee\\x9cv\\xe5\\xed\\xf8H\\xf1\\r\\xce\\x1a\\xe4x\\xd7\\x93\\x9b\\x96\\xac\\xd3\\xdd5%\\xd2\\xc9\\xbc\\xe3\\xa54\\x1eI\\xca*\\x88\\xe2O\\xb6\\xa0Zt\\xebP:\\x05\\x00\\xb0)G\\xed?:d~\\xbax\\xf5\\xa98\\xb2\\xf1!\\xbcN\\xf5\\t\\x9f\\x0b\\xee\\x8c\\xff\\x00mU\\xe8\\xc6<\\xf4IW\\xd2\\xb5\\x1c\\xc5kU\\x92JT\\xae\\xf0\\xa70\\x90I\\xcfA5\\x1f\\xc4JTh\\x9a>\\x94l7\\xa30 \\xd6\\xad\\\\\\x0f|u\\xcb8\\xa3^\\xe9\\x1b\\x10X\\xf9\\xb2\\xb3#C\\x03\\xce\\xb4B\\xc9_\\x896d\\xfa#_3Y\\xb0\\x1f\\xdf\\xe1\\x98\\xe7\\x95S\\xdd\\x01X\\x04\\x02L\\xd3\\xad\\xa0R\\x84\\x8f\\xbb:!\\xdf$)Bu\\x15\\n)\\x04\\xc4\\xc6q\\xe5UC\\xb9\\xef\\x10\\xca\\x92\\xb5\\x18&br\\xab~\\xa5\\xec;\\n1\\xdaF\\x1c\\xac\\xf2\\xb4\\xb8\\x89\\xfd\\x88\\xfbi\\x95\\x8c\\xe3\\xb16w\\x12J\\xf0\\xbbv\\x8a\\x8a\\x1cB\\n\\x02\\x89\\xcaA\\xcb\\xdd\\x15\\\\\\xb9\\xd2\\x93\\x838\\xe3\\x81\\xccA\\xd5O\\xd4H\\x1e\\xcaZ?\\x85\\xfel\\x9c\\xeaI\\xe0\\xe5P,\\x1a\\x90\\xc9\\x81JD\\xb99V\\xa0\\xa9\\x96y\\xad\\xa1\\x9e\\xa3J\\xcd+i\\x1c\\xfd\\xf5\\x82\\xe7k]\\xa3\\xc6.\\xaf\\xd0\\xf6\\x0f\\xb0F\\xc9\\xa7\\x14\\x14\\xbb\\xbc^\\xed-\\xae\\'2\\x1aL\\x9d\\xe8\\xae6\\xbdQ\\xeaV\\xad\\xf7\\x96\\xbc\\xbcJ\\'!\\x1e\\xea\\xc5l\\xe2A<h\\xd8:\\x81H<\\x90=\\x94#\\xa2*\\x05\\x8eB\\xa2\\xf3\\x98\\xce\\xda\\xe0\\xf8V\\x1a\\xfd\\xe5\\xa6!g{v\\xd4\\x06\\xad\\xd9t)Jr`H\\x1c\\x01\\xcc\\x9e\\x94K-\\xd3W\\xa7\\x9c\\xf4rm\\xcb\\x8e+\\x12\\xb9S\\xe0\\x87\\\\y\\xc5\\xaf\\xc3\\xba7\\x8a\\x899p\\xd6\\xbd3O&R\\xc1\\x85K\\x8d\\x9eG_]h\\x03\\x8e\\x10\\xe2\\xe0\\x9c\\xf7\\x81\\x1c\\x0eu\\x02w\\x8e\\xa7:\\x96\\x80e\\xccq\\xe7J\\x90\\xa09A\\xf5\\xd1N\\xf6\\x8d~\\x0f\\xcd\\x1c\\x06\"#\\xdfH\\xd8\\xec\\x8c\\xda440\\x0cOST\\xdc:<\\n\\x8a|9q\\xd6(\\x9cq@\\x891\\xcez\\xd4\\x8b[N\\xa5\\xb6\\xdd[p\\x85\\xce\\xea\\xb8*5\\xaa\\x13fA\\x1a\\x8e\\xa0\\x0c\\xab?T\\x00\\x9d\\xe1\\x133\\x91\\x06k[F\\xae3e[\\xd3:D\\xe9\\x9d;Og\\xd8\\x91\\xff\\x00\\xac\\x1bE\\x02e6O\\x99\\xf3H\\xa9\\x8c\\x9d?\\x81\\\\\\x92\\xd5\\xc2$\\xe4R\\xa0@&8LN\\x9aMc\\xa9\\xeep\\xf6X>\\xe1\\x95J\\x82\\xb2\\x02A\\xcbJ\\xd6\\x1e\\r[a\\x8b\\xfcY\\x15\\xa4\\x9c\\x15\\x95@\\xb0\\xba\\x90\\xca\\xb2\\xa5\"\\\\\\xaa\\x98\\x16\\x16\\'\\xe9\\x9b\\x8d\\x7fu\\x14\\xac\\xb7\\x93\\xf5\\x91\\x9f\\x18I\\x8a5}\\xc3\\x1bH\\x98<k\\xcc\\xf7$\\xb7\\xa7J\\x11\\xe4\\xe5F\\x91\\xc4\\xe9P<\\x93\\xca\\x84X9g\\x15\\'\\x8d\\xedGh\\x95\\x82\\xe0\\xad\\xda[\\xb8\\x1b\\xbb\\xc47\\x90\\x97\\t\\x8d\\xc6\\xc4o\\x90y\\xe6\\x07\\xae\\x8b-\\xe2:\\xf4q\\x96\\xee\\xfa2\\x0ba\\x85\\x1bK\\xb1v\\xbb\\x91p\\x12\\r\\x9f\\xcdV\\xdfv\\x15\\xc4:\\x15\\x9cuMs\\xec\\xcb\\xd9\\xe9\\xcb\\x9fW\\x99\\xbf\\xc1\\xd3ut]B\\x8a\\x1dQ\\xfc\\xa2\\x1cL\\x91\\xd7\\x81\\xad\\xe3\\x97S\\x1f\\x10g\\xd2\\xe9\\xe7\\xe5\\xe3\\x9c\\xbd|_-\\xa6]\\xef\\x1bJ\\x88\\x05hH9q\\xaf_\\xa6\\xeb\\xe5\\xf51\\x98\\xe5d\\xf0\\xbc\\xc3P\\xdd\\xdd\\xb6\\xfb\\x89\\xf1\\xef)>\\x1c\\xa6\\x0eU\\xd3\\x194\\xe5\\x95\\xb2\\xa4\"\\xcd\\xa7\\x10$-\\x1d\\x08\\x06\\xadr7T\\xe2\\xe0\\x8b\\xdb\\x86V\\x84n6\\xb2\\x80\\xa0\\x08$\\x03\\xc6\\x8b5O\\xa2F\\xfaD\\xf8E\\x16J\\xb7u\\xb1\\x96\\x1a\\xb9\\xb7P\\'t\\x19\\x069s\\xce\\x99\\x88\\xde\\xa7\\x00\\xdb-\\xb2\\xdaZI1\\xa09O\\x9e\\xb4\\xd96v{\\xbbox\\x00\\xb2\\x14O\\x04\\x9f\\x85\\x12\\r\\xbd\\xae\\x07\\xd9\\xae!\\x8d\\xe0V8\\xad\\x96+\\x86%\\x9b\\xc4\\x15\\x06\\xd7\\xde\\x15\\xa3uE$*\\x01\\xcc\\x11\\xa7QZ\\xec\\x83\\xba\\xfb(v\\xb7e\\xee\\xb6g\\x17\\xfc\\x1d\\x88\\xbc\\xc3\\x8f\\x1bv\\xee\\x02\\xd9Z\\x94\\x92\\x85\\xccj\\x01\\x07#\"\\xb3q\\xd7\\xa9\\x95\\xe7\\xd6\\x86\\x82\\x12w\\xe38\\xdd\\x9c\\xfc\\xea\\xedV\\x9bR\\x9bBU\\xbc\\xa2z\\x03F\\x8e\\xc8t \\xb6R\\x14G2H\\xa6\\x1d\\xb7\\x0e\\xcap,.\\xc6\\xc58\\x85\\xb3*V Gt\\xa7\\xd6\\xa2HI\\x19\\x84\\x8d\\x00\\xac\\xefnw\\xea\\xd2\\xf0\\xfb\\xbe\\xe2\\xf1\\x11\\x12\\xa0R\\n\\x809\\xc7\\xbfJ3\\x9cm\\xac<\\xaf\\x82\\xa1\\xb2`\\x81\\xaez\\xd6\\xb0\\xf0\\xd6^W\\xd8a\\xfcY\\xbf*BjUR8\\x15R(\\x9c\\xa9H\\x97\\x071Z\\x81eb@u\\x04\\x90 \\x1a\\xc9K/:\\t\\x000@\\xe3N\\x83%A\\x94\\x89\\xaf#\\xdc}\\n\\xd3\\xad\\x00\\xe8\\xa9\\x1dB\\x86\\x93EC/%\"I\\x14#/b\\r4\\tR\\xd2#\\x8dE\\x92\\xf6\\xc7\\x8b3\\x88\\xdda\\x0c[\\xa9+r\\xdd.\\xadgx\\x01\\n \\x0f\\xee\\x9a\\xef\\xd1\\xe9\\\\\\xa5\\xb1\\x8b\\xd7\\xc7\\xa5u\\x93;=\\xe6\\xf1\\xf0&z8+\\x7f\\'9\\xe8\\x7f\\xd5\\xf4\\xc1\\x05\\xc0\\xb4\\xa85&x-9{\\xe8\\xbd\\x1c\\xbd\\x9a\\x9f\\x17\\xd3\\xf7y\\xa6\\xb6}\\xee\\xf5jz\\xd9+\\xdfQ9(\\x1c\\xa7.5\\xbe\\xcc\\xb4\\xf1\\xe5\\xd4\\x99[b[X(oKw\\xd2s>\\x07\\x15\\xf7\\xd51\\xca3\\xdd\\x0b8R\\x86h7\\xe8<!d\\x9agw\\xb2\\xe3{E88\\x0e\\xa9JE\\xd2\\x94\\xa9\\x95(\\x995s\\xea6\\x07\\x0clj\\xdb\\xc0k\\xa2\\x85\\x1e9; \\xe1\\xcd\\x82\\t\\x0e\\xe5\\xcei\\x9c\\xabI\\xf9\\x83@AS\\xbc5&\\xa1)\\x06\\xc5\\xa8?L\\xe7-j\\\\\\xfa\\x140\\xa6\\x17\\xe2.\\x99\\x9c\\xcf\\xaa\\x9d\\xfb\\x1d\\xd19`\\x86\\xc1\\t}P\\x07\\x9cU\\xaeV\\xcd\\xfc\\xcco\\x03\\xdf(\\xe5\\xc62\\xa2\\xabG\\xf3w\\x03\\x0f4\\x8b\\x95\\xa5\\x0e\\xa5)p\\x00\\x0c\\x80dO\\xae\\x9diL\\x81\\x8c;\\xbc)l\\xba`\\x881F\\x95\\xbe\\xcd\\xa7\\xb3\\xf7\\x1ej\\xc1M4\\xb4\\xee\\x80\\x0f\\x893Y\\xd0z\\xebK\\xf51\\x8a\\xd8;r\\xb4\\x06\\x13p\\xd7zR \\x84\\x15\\x00H\\xcf\\x91\\xa7[\\xe0o\\\\\\xb4lR\\xc56/\\xbfn\\x85\\x95\\xa5\\xa2\\x00Q\\x10N@\\xe7\\xed\\xa3\\x19\\xa9\\xa7K\\xca\\xc6\\xc0\\xc5\\xb3~T\\xa4\\xc0\\xaa\\x81AU\"\\xd4\\xaaR+\\xaa\\x95\\x8f:\\xd0X\\xb4\\xad\\xd1;\\xbb\\xd9q\\x13Y5-7P\\x90\\x03j=dgHe-(\\x04\\xe7\\xef\\xaf-{Aw-\\xa0I#\\xdbBC\\xba\\xc7-\\xad\\x91\\xbc\\xeb\\xa8O\\x99\\x8a\\x96\\xd1\\xad\\xf1[\\xdcD\\xc6\\x13\\x87\\xdd]\\x0f\\xd3\\x08\\xddG\\xf4\\x8c\\n\\xc5\\xb0\\xc9j\\xd6\\xdbg\\xb1\\x9b\\xc0\\x15\\x7fyod\\x93\\xaa\\x1a\\x1d\\xea\\xfd\\xb9\\x0f\\x8df\\xdff\\xa6+{=\\x93\\xc2\\x99!w\\r\\xbbz\\xe6\\xbb\\xd7+*\\x1f\\xd1\\x10+7t\\xc9#(\\xf9A0\\xdb;A\\x81\\xf7\\r6\\xd2\\x15\\x87\\xad;\\xa8@H\\xc9\\xde\\x9eu\\xf4>\\x0b\\xee\\xd7\\xcf\\xf8\\xcf\\xbd\\x19q9\\x18\\x9c\\xb9W\\xad\\xe4\\xd6\\x80r\\x9c\\xe7ZT\\xb0`\\xe8xPJ\\xd4\\x8c\\xa7\\x8dQR\\xa4\\x11\\x99\\xa8\\xea\\x0c\\x131&jP7\\x8f\\xe9+\\x9e\\xa6\\xad\\x1d\\x81R\\xa4\\x8d\\xe3\\xed\\xa1l\\x9d\\xf23\\xdeV\\xba\\xf3\\xa7[\\x1b\\r\\xe3\\xc6O\\xb2\\x8dF\\xb7H*9\\x1c\\x87\\xaa\\xadA\\xbaB\\x80:\\xa5&:\\n5\\'\\xa3[%a;\\xa4n\"8\\xf8G\\xddWl\\xac\\xdamHl\\xcc\\xb6\\x828\\xf8ES\\x19\\xe0\\xec\\x98m\\x02Cm\\xa6\\x04\\x93\\x02\\xae\\xd8e\\xbe\\x89vx\\xed\\xfd\\x93m\\xaa\\xca\\xfd\\xd6\\x10\\xec\\x86\\xca@\\x01q\\x12\\x04\\x88:\\x8fh\\xac~\\xc5\\xe0\\xde\\xe8]\\xf6\\xd4c\\x8a\\xb2x/\\x12u^\\x03\\xaaS\\xf7V\\xe7O\\x1d\\xf8b\\xe7t\\xeb-\\xa6Y\\xf9\\xf3\\xe8.\\xf8\\xe5\\xa0\\xb5j}\\x04\\xc9\\x8a\\xf2\\xfe\\x0fV>\\x9b9b\\xf2C \\x15\\x03\\x04\\x80B\\xa6G\\n\\xa71Y\\xcf\\tiu&3\\xf7\\xd6\\x86\\x8e\\x079\\x1f}@\\xa59\\xe7JU\\xe3X\\xa20\\xcb\\x15\\xdd\\xb8\\xcb\\xcf%\\n\\x03u\\xa4\\xef\\x1c\\xcc\\x02zN\\xa7\\x857~\\x8ak\\xd558\\x93\\x8e\\xe0vw\\xa2\\xd5\\xe6V\\xf8J\\x94\\xc2\\xc0+D\\x83 \\x83\\xadc+\\xa39\\xf0\\x03\\x17D\\x08u\\xb08\\r\\xe4\\xe5Y\\xef]\\xac\\xb5\\xb71\\x8b\\xd0M\\x95\\x82\\xd0\\xd9\\xd1\\xcb\\x83\\xdd\\xa7\\xdf\\x9f\\xba\\xb8\\xdc\\xf1\\x8f\\\\\\xc6\\xd4\\xc6vV\\xe6\\xe0\\x85\\xe2x\\xb2\\xf7\\x7f\\x8b\\xb4D\\x7fY_us\\xbdK\\xe8\\xdc\\xc2.p\\xbd\\x9e\\xc20\\xf7\\x03\\x96\\xf6-\\xad\\xe1\\xfeu\\xf9u~\\xd5i\\xea\\xac[\\xbf-kOF\\x85\\x95\\x01\\'!\\xc3\\x85^\\x11\\xc4E\\x08\\xfas\\x00\\xd4\\x98\\xaf\\xca5\\x11\\x88\\xec\\xdb\\x90s\\xb7\\xb8O\\xb1h?m{\\xfe\\x0b\\xc6_\\xc1\\xe0\\xf8\\xcf8\\xd6@OI\\x9a\\xf6\\xe9\\xe2((\\x9e_\\x18\\xabHi \\x8c\\xbd\\xb4\\x18T\\xe5\\xc8\\xf0\\xa8\\x96\\t\\xebW\\x84\\x1a\\x89\\xd6\\xaa\\xbc\\x81\\x89\\xe0j\\xd9$\\x9c\\xc1\\xaa\\n\"s\\x9d\\x0c\\xd4<\\x81\\xd23\\xd3\\xd9SD\\x1ecJ\\xaa\\x9b\\x02\\x04\\x904\\xa3d\\x83\\x12M@D\\xfa\\x8dK\\xc9#t-*R\\x1bu!@\\x948%*\\xe6\\r\\x19c\\xdd,k\\x1b\\xdbv\\x97\\x8bc7X\\x8c\\xb6\\xe2\\x19M\\xb0\\xc9\\xb6\\x93\\xe8\\xb69\\x81\\x00o\\x1e$@\\xe4+\\x86?\\x0f\\xab\\xbb]s\\xeb\\xcb\\xc4\\x8a\\x87\\x12\\x167H\\x92\\xb5${T\\x07\\xdb^\\xa9\\xe5\\xe6\\xbe\\x1d\\x8d\\xf2\\x82p\\xd9vE\\xb4\\xf7\\x96\\xc4\\xb1t\\xd3-\\x96\\xdfo\\xc2\\xb4\\x92\\xe2\\x13!C0s\\x8a\\xf1G\\xae=\\x96\\x1bh\\xcap\\xdb@\\xed\\xbbJXe\\xb0\\xa5)\\xb0I;\\xa2d\\xf3\\xa9\\x99\\xe1([Z\\xf1\\xb6c\\xff\\x00,TG\\xf3[_\\xff\\x00\\x19\\x9f\\xe8\\n\\x08\\xcd\\xa5\\xa1\\xd6\\xdd\\xaf\\xe8\\xd4\\x99\\x9e\\xdf\\\\_1\\xda\\x8e\\xc3\\xe0\\x98=\\xfb\\xf8u\\xb6$nUv\\x18JT\\x16\\x10\\xd9RrP0$g\\x11Z\\xdf\\x07\\xc4^\\xe2\\xf8\\x0e4\\xe3E\\t\\xb9b\\xfa\\xdd*\\nJT\\x88pe\\xaeyH\\xaeyL\\xa9\\x96E\\x1a\\xaco\\x10\\xa2\\x85Yb\\x8a)\\xc8\\x91n\\xb2\\x0f\\xba\\xb9\\xea\\xb5\\xb8\\x86\\xdb\\xed\\xdc2\\xd3\\xec\\xba\\x87\\x99u\\x01m\\xba\\x85\\x05%i:(\\x11\\x91\\x15\\xc7Z{i\\xf6\\x14A\\x89\\xc8\\xd0\\x93\\x123\\xa8T\\x86L\\x115$\\x94\\x98\\x8a\\x91\\xe4\\x9c\\xaa\\xd2c\\x9f(\\xc4\\xca6i\\xc0`\\x85\\\\\\xa3\\xdc\\x83^\\xcf\\x82\\xf3\\x94\\xfc\\x1e/\\x8d\\x9cb\\xc6G\\x0c\\x8f\\xb6\\xbd\\xef\\x06\\xca\\x1a\\x03\\x9f\\xae\\x8d\\x93\\x83\\xcc\\xd4J\\xf5\\xd46P\\x12\\x06\\xa2\\xaeZ\\x83\\xe1\\xa9\\xf5\\xd0\\x85\\xa6u\"\\x14`\\x8a\\x95\\xe2\\x90H\\x9e\\x15\\xa0\"}*\\x113\\'-M5@\\x99\\x15\\x9a\\xd0\\x8e\\xa2t\\xd7\\xad\\x1e\\x88\\\\~\\xea\\xb6\\x89<3\\xf5\\xd2\\x8c\\xace\\xa6u2F\\xee\\xf2\\xd2\\x93\\x92J\\xd2\\x0c\\xf2\\x91U\\xe1_f\\xd7\\xdb\\xc6)wq\\xd9\\xb5\\xdd\\xbd\\x95\\xff\\x00z\\xcd\\xc3\\xec\\xa1\\xc1\\xf3\\x82\\xb0\\xa6\\xc1\\xe1\\x07?\\x10O\\xbf\\x95x\\xa5z\\xa5\\xad;\\x00\\xc4n\\xed\\xdc\\xc2\\\\z\\xe5\\xfe\\xe1\\ti\\xb7\\x11\\xdf\\x156AHI\\x1a\\xc1\\xe9T\\xa1\\xa4\\x19\\x04\\x89\\xd0\\xc5i\\x0eMI\\xe5v\\xc6\\xe6\\xfe\\xce\\xf2\\xd1\\xfbk\\xc7\\xd9a\\xc6\\xca7P\\xa8N\\xf8<r\\xe2\\x0f\\xb8\\xd1|pX\\xae\\xd0\\xe3\\x18\\xc3\\xfd\\xbc\\xec\\xb1q\\xe7\\xd6\\x9b[u\\x96\\x96#y2\\x95\\x85\\xe5\\x1a\\x19\\x00\\xeb\\x95\\x1d\\xdf\\xb2\\xabm\\xd8\\xecb\\xee\\xea\\xe1\\xfb\\\\E\\xe7\\x1eZ\\x87z\\xd2\\x96\\x90\\x08\\x03\\xd2N@t\"z\\xd1/\\xb9z\\xcc\\xfa\\xd3\\xb5\\xa7\\t\\xec&\\xdc\\xdf\\xec\\x8d\\xc7u\\xba\\xab\\xcc\\x19k\\xdez\\xc8\\xaa\\nI\\xd5m(\\xfa\\n\\xe6=\\x15q\\xe7VxL\\xbf\\x17\\\\:\\xbd\\xbc_\\x0e\\x89\\xc01;\\x0c{\\x0bk\\x12\\xc1\\xae\\xd1uf\\xe1\\xdd\\xde\\x02\\x14\\x85qB\\xd3\\xaaT8\\x83\\xe6$g^l\\xb1\\xb2\\xea\\xbdR\\xeen.X*)\\x85\\x03\"\\xb0\\x8e\\x03\\xbaG\\xc2\\x94\\x9c\\xd3.-#\\xc3\\x03\\x99\\xca\\x84\\x94\\x96\\x02G\\x89S\\xd0T\\x99\\x1f\\xca9\\xb4\\xfe\\x08\\xd9\\xc7\\x12\\x91\\xe1\\xbby\\'\\xd6\\xd8?e{>\\x0b\\xefe\\xf8<\\x7f\\x19\\xe21\\x08>q^\\xe7\\x83E\\xc4\\xc5\\rrq)\\xd2\\x07\\x0eUmj\\x94Dg\\x03\\xed\\xaa\\x1a2\\'\\x96\\xb5-\\x01\\xcb8\\x81V\\xf6\\x89Vs\\xc8\\xd4\\xac4\\xa3\\x9eb\\x98/&\\x94r\\xe1H\\x11:\\xc5KB\\x9c\\xc6dP\\xa4*g\\x81\\x8d5\\xd2\\x8f\\ry\\x14\\x91\\x9c\\xd4\\x8a9yr\\xa8\\xd2\\x0e\\x99L\\xc5\\n\\x9bX\\xcc\\xfb)\\x81#\\x04i.\\xe3\\xf8SjJT\\x87/XA\\x04HP.\\xa6F|(\\xca\\xf1V\\xb9u\\xe6)\\xb2\\xfb5\\xdf\\xafsg\\xb0Pw\\xc8\\x91b\\xde\\x93\\xe5^\\x1d\\xbdr\\x1eF\\xc9l\\xaa\\x82O\\xf97\\x83\\x82\\x92\\x08)\\xb6\\t\\x823\\x07*v\\xce\\x9e\\x90\\xdc\\xc9\\xcc\\xc9\\xabgE\\xa5\\xe9\\xabkH\\xd8\\xbe\\x1ba\\x8c\\xd9|\\xd3\\x15\\xb5n\\xea\\xdbx8\\x1b\\\\\\xc0P\\xd0\\x88 \\xf15\\x9bZ\\xd3\\xcf\\x9e\\xcf\\xf6I\\xcb\\xd4>\\xac\\x0e\\xdf\\xbfG\\xa2\\xefx\\xe6\\xfa|\\x95\\xbd\"\\xa5b\\xe3\\r\\xd8\\xfc\\x07\\x0f\\xc4\\x18\\xbe\\xb3\\xb3u\\xbb\\xa6\\t-\\xac\\xdd\\xbc\\xad\\xd9\\x10r* \\xc8\\xadF^\\x87*\\xb4\\x9f5\\\\\\xbf\\x06w\\'\\x9e\\x7fmu\\xd3\\x0bm\\x8f\\xda\\xfcWeqo\\x9f`\\xef\\x06\\xdcX\\tu\\x87\\x06\\xf3W\\t\\x1fUi\\xe3\\xd0\\x8c\\xc7\\x02+9\\xe32\\x9c\\xbaa\\x9d\\xc2\\xfd\\x1d_\\xd9\\x8e\\xd6\\xe0\\xbby\\x87\\x1b\\x8c=\\xd5[\\xdf2\\x9d\\xeb\\xac5j\\x1d\\xeb<7\\x81\\xfa\\xed\\xcf\\xd6\\x03\\x8e`\\x1a\\xf2u0\\xb8yz\\xf1\\xcee7\\xfa\\xfd\\x7fw\\xb8\\r\\xb6\\xd1\\xf0 \\x03\\xce3\\xaem\\x94\\x15N\\x80\\xcdZL\\xb3\\xe5\\x0c\\xde\\xf6\\xcb`\\xeeG\\xa1\\x88\\x91\\xedi_uz~\\x13\\xef\\xdf\\xc1\\xe5\\xf8\\xb9\\xfb2\\xb0\\x90\\x8c\\xe7\\xdc+\\xe8<\\x1a+tA\\xe1\\xf6Q\\xcf\\x93 \\xe2\"FU\\x1b\\x06G\\x19\\xf5\\xc51h~\\xef]\\x06\\x8b\\x86S\\x96y\\xf1\\xa5\\x92N\\x82?\\xc1\\xa8\\x9aP\\x9d&\\xa1H#Z\\x84\\x12\\x93 \\xc8\\x10i\\x86\\xc23\\xde\\xeb\\xcc\\xd5yZ\\xe4|>\\xea\\xca\\x17\\xb2\\xad\\x91\\x8dI\\x03N\\x15\\x1b\\xc0\\x8c\\x1c\\x8d@\\x85I<FunD\\xb2\\xd9V\\xf7\\xf6\\xb3\\x02O\\x13\\x881\\xfe\\xf0V3\\xbf\\xb3Z\\xc6s\\x1dkwpM\\xc2\\x8c\\xfdc\\xf1\\xaf\\x13\\xd9\\xa3\\xed]H\\x11S:Hi\\xf9\"\\x9d\\xad%\\xb4\\xe14l\\xe9%\\x04\\xc6tmh\\xa4\\x18\\\\\\xd2\\xb4\\x8b\\xb4\\xbbM\\x83\\xec\\xa6\\x0e\\xacOh/\\xda\\xb2\\xb4N@\\xac\\xca\\x9cW\\xe8\\xa1#5+\\xa0\\xad\\xc9\\xb6u\\xbf\\x0cU\\xff\\x00\\x95\\x16\\x0e\\x97\\xdc\\x0cl\\xbe\"\\xe3AD!k\\xb8m\\x05I\\x9c\\x89NpzNT\\xea{\\xadO\\x7f\\xd7\\xf3r89\\xf5\\xd2\\xba9\\x96\\x0f\\x9c\\x1dG*=\\x0c\\xf2\\x9f\\x84\\xe2\\xb7\\xd86#m\\x89a7/Z_\\xdb/}\\xa7\\xd9T)\\'\\x8f\\xab\\x81\\x07\"25k|V\\xa6W\\x1ec\\xab\\xfb!\\xed~\\xc3m\\x03XN5\\xdc\\xe1\\xfbK\\x92R\\x01\\xddf\\xf7\\xabs\\xe8\\xaf\\x9a8\\xea\\x9eC\\xcb\\x9fJ\\xcec\\xd3\\x87Re?_\\xaf\\xcf\\xf1\\xf2\\xd5\\xcby\\xf5\\xe2\\rrl;\\xb28ME\\xe3{T\\xd9\\xeb\\x8d\\xa4\\xd9\\x96\\xed-^e\\x87m\\xeeSs.\\xa5D(\\x04\\xa8\\x14\\x8d\\xd13\\x9du\\xe9u>^]\\xce]\\\\/S\\x1dG>\\xdc\\xe0x\\xbd\\xba\\x94\\x95\\xe17\\xe6>\\xb2\\x18Q\\x06\\xbd\\xb3\\xad\\x8d\\xf5x\\xafK)\\xe8\\x88\\xbb;\\xd4\\x1f\\x1e\\x1d\\x88#\\xce\\xddT\\xfc\\xdco\\xa8\\xec\\xa6T\\xdb\\xa9\\xf4\\xadnQ\\xe6\\xca\\x87\\xd9Z\\xef\\x8b\\xb2\\x90\\xa5\\x80|IZOV\\xd5\\xf7S3\\x83\\xb3\\xd4\\x82\\xfb`\\x89Tg\\xa9I\\xa7c\\xb4_8fc\\xbdD\\xf54wE\\xda\\x1d\\xf3GG[=g\\x85;R\\tN7\\x97\\xd2\\'\\xcej\\x95XN\\xf2~\\xaa\\x93\\xed\\x14\\xec~\\x01\\x96\\x80\\x88=h\\x94\\xebD\\xa8\\x10GN\\xb5lh\\x922#3U\\xa7Z%S\\xa4t5 \\x9e&\\x84I1H%f\\x0f\\x11\\xc6\\xab\\xa3}\\x97\\x9b\\x0c\\x8e\\xf3n6y9g~\\xd7\\xb8\\xcf\\xd9X\\xce\\xfe\\xcd8\\xf9\\x8e\\xa0zJ\\xc9\\xeak\\xc8\\xf5\\xed&\\xdd\\xa5\\x9d\\x12s\\xa2\\xa4\\xf6\\x9aP\\x82h\\xda\\xd2cr:P\\xd6\\x8f\\xa3yY\\x0c\\xe9\\x81\\x8d\\xf6\\x9d\\xdb\\xc6\\x0f\\xb3\\x05\\xec?fC8\\xce2\\x99Ip*mm\\xd5\\xfa\\xca\\x1e\\x99\\x1c\\x93\\x97^\\x15\\xb9\\x8f\\xb8\\xba\\x9e\\\\\\xbf\\xb4\\xdbO\\x8b\\xedN.\\xbcO\\x1f\\xbfz\\xf6\\xedF\\x12\\xa5\\xfa-\\xa7\\xf4[N\\x88OAZ\\xac[\\xbb\\xca\\xb7\\xbf\\x03#\\xad\\x1d\\x83j\\xd4\\x99\\xcb\\xa6u\\xd3l\\x94\\x9e\\x1c\\xc7*t6q\\',\\xe0\\xea#\\x9d\\x17S\\xc1\\x94`\\x90\\x12R!_\\x03D\\xf2f\\xe71\\xd1\\x9d\\x8fv\\xe7\\xbam\\xf0M\\xbe\\xb9*L\\x06\\xed\\xf1\\x85\\xe6S\\xc0&\\xe3\\x98\\xe1\\xdej>\\xb4\\xe6G\\x1c\\xfa~\\xb1\\xdf\\x0e\\xa6\\xf8\\xbe\\x7f_\\xafo\\xc2\\xf9\\xe8\\xe1\\x05)RHRT\\x02\\x92\\xa0d(\\x1d\\x08#\"\\x0f:\\xe3\\xad;\\x014\"\\t<\\xe8\\xd2\\x0f0*\\xd2$\\xa1\\x07T \\xf9\\xa4U\\xa4B\\xad\\xad\\xd5\\xe90\\xc9\\xf3l\\x1at\\r+\\x0f\\xb1_\\xa5ej|\\xdaO\\xddH\\xd42\\xbc\\x0f\\n_\\xa7\\x85\\xd8\\xab\\xcd\\x84\\xfd\\xd5n\\xfb\\xadOdu\\xec\\xbe\\x02\\xbfK\\x06\\xc3\\xcf\\xfb:~\\xeaw}\\xc7f>\\xc8\\xeelf\\xcd\\xaf\\xd2\\xc0\\xb0\\xf3\\xfe\\xa4U\\xdd\\x94\\xf5]\\x98\\xfb\\x18^\\xc1l\\xaa\\xfd,\\x06\\xc7>M\\xc5=\\xf9\\xfb\\xae\\xcc}\\x91\\xd7\\xd9\\xc6\\xc8\\xab\\\\\\x06\\xd0O)\\x1fm_3?q\\xf2\\xf0\\xf6Gs\\xb2\\xfd\\x8fW\\xfd\\x8a\\xd0\\xf2Z\\x87\\xdbO\\xcd\\xcf\\xdc|\\xac=\\x91\\xdc\\xec\\xa3c\\x953\\x85\\x11<\\x9eX\\xfbj\\xf9\\xb9\\xfb\\xaf\\x93\\x87\\xb2:\\xfb\"\\xd9\\x15ib\\xf8\\xf2\\xb8U_7?u\\xf2p\\xf60\\xae\\xc7\\xf6S\\x85\\xbd\\xda|\\xae\\r?;5\\xf2q2\\xbe\\xc7v]Y\\x81|\\x9f\\'\\xff\\x00u??6~N\\x06U\\xd8\\xc6\\xcd\\x92w^\\xc4G\\xfa\\xe0~\\xca\\xbe~k\\xe4`\\xb8\\xd9n\\xcc0L\\x13\\x16f\\xf1\\x9e\\xf5\\xe7[2\\x8e\\xff\\x00u[\\xa7\\x82\\x86Y\\x11\\xce\\xab\\xd5\\xcb.(\\xf9X\\xe3\\xe1\\xef\\x05\\x8a\\x11\\xa0\\x04\\xf35l\\xe8\\xe8D\\x00\\x0f\\n\\x08\\xc6U%&\\xd9m~\\x07\\xb1x_\\xcf\\xf6\\x86\\xf56\\xe8T\\xf7L\\xa7\\xc4\\xf3\\xc7\\x92\\x13\\xa9\\xf3\\xd0q4\\xc9o\\x85\\xf5\\xbe\\x1c\\xab\\xda\\x97mX\\xde\\xd9\\xf7\\xb8}\\x81^\\x11\\x80\\xab\\xc2m\\x99\\\\\\xba\\xf8\\xfeUcQ\\xfa\\xa3.s]\\xb1\\xc2NX\\xbdOLYN\\xf4\\x002\\x8e\\x9c)\\xaea\\xbcg:\\xb4\\x85$\\xe82\\xf2\\xa4l\\xdc\\xc9\\xd3:\\x95-*\\xce8Ug\\x00\\xe2NY{\\xbc\\xaa\\xfcIGM\\'\\x8f*\\xbdN\\xb8\\x10\\xd38\"\\xaf\\n5\\x8e\\xc8\\xbb`\\xc4v)max\\xaa_\\xc4\\xb6o{&&]\\xb5\\x9dK$\\x9d8\\x94\\x1c\\x8f\\x08$\\x9a\\xe7\\x96\\x13\\'\\\\z\\x9a\\xe3/\\xd7\\xfc\\x7fo\\xe8\\xeb\\x1c\\x17\\x18\\xc3\\xf1\\xec)\\x8cO\\x05\\xbcj\\xf7\\x0f|}\\x1b\\xcd\\x1c\\x89\\xe2\\x92\\x0eiP\\xe2\\x93\\x98\\xaf=\\x96yw\\x953z\\x849\\xa9\\x0c\\x1a\\x90MHu!T\\x8d^]1eh\\xf5\\xd5\\xe3\\xc8b\\xd9\\x94\\x95\\xb8\\xea\\xcc%\\t\\x1a\\x93\\xd2\\xa4\\xa8ck\\xf6i\\xff\\x00\\xc9m\\x0e\\x14\\xa9\\xe7r\\x13\\xf1\\x8a\\xd7m\\xf6=\\xb5`\\xc6+\\x87?\\xf9\\xbe\\'\\x87\\xbb\\xfb\\x17M\\x9f\\xb6\\x85\\xdb}\\x92\\xd3\\xf4\\x99\\xb6R\\xb1\\xcd\\n\\n\\xf8\\x1a6\\xb5}\\x86Zw\\xf8\\xb7?\\xa2h\\x04(\\x14\\x98P \\xf5\\x15\"b\\xa4\")D\\xeegP\\x1e\\xed1\\x16\\xd0\\x85\\x83\\xd6\\x98\\xcdJQ\\x9a\\xd3$+$\\xa9J!)H\\x95(\\x98\\x00s\\'\\x85G\\xcb\\x0c\\xedC\\xb7\\xec7\\x05\\xefp\\xed\\x8c\\rb\\x98\\x90\\x94\\xaa\\xf5Y\\xdb\\xb2\\x7fW\\xf8\\xc3\\xfd_:\\xde8o\\xc8\\xb9Lx\\xf3\\\\\\xc3\\x8fcx\\x8e\\xd0bn\\xe28\\xd5\\xeb\\xd7\\xd7\\xaezO<\\xa9>@h\\x07  Wo\\x1e\\x1cr\\xca\\xe5\\xcdW\\xcc\\xfa\\xf8P\\x03Z\\x90\\xa4k\\xf1\\x14\\x00\\xf5\\x9a\\xb4E\\xc7>\\x02\\x90\\t9\\xd4\\xa1\\xf6\\xd5\\xe7\\xa6\\xb5RYQQ\\x1b\\xc6\\r;\\x10b8\\xe59\\x1e\\x94i\\xa8RO\\x1c\\xb7\\xb8\\xcf\\x1e\\xb5h^\\x1e\\xab\\xb3\\xed\\xba\\xc6\\xb6\\x13\\x167x;\\xc1V\\xee\\x98\\xba\\xb2t\\x92\\xcd\\xc2G\\x05\\x0e\\n\\x1c\\x143\\x1eR+9Icx\\xe7q\\xe1\\xd7\\xbd\\x9f\\xed\\xce\\r\\xb7xI\\xbb\\xc1\\x9cR.Z\\x00\\xddX\\xbaG}ny\\x9f\\xd2A\\xe0\\xb1\\x91\\xe3\\x07*\\xe1\\x96\\x16;\\xcc\\xa5\\xf0\\xf4\\xf5\\x8d\\xb49\\xa5\\x0ef\\x80P\\x06\\x92P\\xa93\\xee\\xdb\\xf1?\\x99lz,\\x90\\xa8s\\x10|6c\\xf8\\xb4x\\x95\\xf0\\x03\\xd7[\\xe9\\xcd\\xe4\\xbd\\x19\\x03\\x18#o\\xdb\\xb0\\xb7\\x1fZ\\x1cZ7\\x94\\x92\\x84\\xa8\\x0eQ\\xce\\xbawYX\\x98\\x18V\\xce\\xb6\\xa4o\\x87\\x99W\\x1c\\x98\\x93\\xee?\\xe3:\\xbb\\xe9\\xed\\xa4\\x7f\\x93\\xee!\\x00\\xb5qn\\x93\\xbd\\xbaBJ\\x92S\\xd7*f|\\x992\\xf72\\xfa\\xf1\\x0c5\\xd0\\x84bw\\x01S\\xa3\\x17n\\x82\\x9fx\\xe9\\xa4\\xd3\\xc6^\\x83\\xbb9\\xea\\xda{\\x11v\\xf6\\xeffo\\xaeo\\xef.\\xae\\xb7\\xef\\n\\x1b7\\x0e\\xa9\\xcd\\xd0\\x94\\x80bt\\xce\\xb8u>\\xf3[\\xb6r\\xd0\\xe2\\xb2\\x87\\x14\\x01\\x11\\xd2\\x94\\x11\\x14\\xa1\\xa7\\xd2\\x152\\xf3\\xdb}\\xb7x\\x0e\\xc2\\xd8\\x0b\\x8cz\\xee\\x1fX\\x96,\\x99\\x85>\\xf1\\xe8\\x9e\\x03\\xa9\\x81[\\x93c\\xc4\\xddroi\\xfd\\xafm\\x06\\xdc\\x97mw\\xff\\x00\\x06\\xe0s\\xe1\\xb1aG\\xc6?\\x95V\\xab=4\\xe9]f29\\xe5\\x9e\\xf8\\x9e\\x19\\xb4\\xf5\\xf2\\xa5\\xccGM|\\xaa\\x1e\\x82\\x9dzRCJ\\xaa\\x80\\xa9\\xe0j\\x82\\x84\\x1e\\x9e\\xda\\x10\\x88=\\rJ\\x95\\x90\\x06Nu\\x1fB\\xd2NS\\x14\\xf0\\x8aI\\xc8\\xc6\\x9eUy\\xe1l\\xb4\\x92S\\xbb&|\\xabTk\\x82\\xb7\\x89\\x07<\\xf9\\xf4\\xac\\x92\\xc2\\x8e\\xf4\\xf0>\\xdau<\\x04\\xfc\\x13\\x17\\xc4p\\x1cR\\xdf\\x13\\xc1\\xef\\x1d\\xb2\\xc4\\x18;\\xcd\\xbe\\xd6Dt<\\x08:\\x14\\x99\\x06s\\xac\\xeb|V\\xb1\\xb6]\\xba\\xc3\\xb2^\\xd70\\xed\\xb4\\r\\xe1\\x98\\xa8g\\x0e\\xdaH\\x84\\xb4\\x0c3y\\xd5\\xa2tW6\\xcf\\xfa$\\xe88\\xe7\\x86\\xbc=\\x18\\xe7,\\xfd~\\xb5\\xfa\\xfa\\xb5\\x1d\\xdc\\xf9\\x1a\\xe4\\xd9@R\\x8a\\x8eU \\xa90~\\xda1/\\xc2;l\\xd5\\x82\\\\\\x01\\xab\\x06\\x92\\xd1$\\xc0\\x0b_\\x89^\\xed\\xd1]z|M\\x8b\\xec\\xaf\\x0e3\\xde$%\\xc0\\x00HH\\x00\\x8f\\t\\xcb/_\\xb2\\x8d\\x10yi\\xb7a\\xc7\\x14\\x02ZnR\\xaf\\t\\xf0\\xf2\\x98\\xcc\\x7f\\x83\\xc6\\xa9\\xbaQ\\x91\\x88\\xd8\\xb8D\\xde4\\x083+\\x98<5#\\xfcE=\\xb7\\xd8p\\xa2\\xc6\\\\\\x0e\\xdd\\x8d\\xd7\\x90\\xebd\\x05\\xef!J\\x8c\\xf9\\x82H\\x04i\\x96\\xb5\\xd3\\x16ro\\x9d\\x90\\xda|\\xdb\\xb3\\xbc(\\x91\\n\\x7f\\xbc|\\xff\\x00\\xa4\\xb3\\xf6Ey\\xf3\\xfb\\xd5\\xa7\\xb0\\xdd\\xa1\\x06\\xedL\\x8a*D>\\xe3V\\xd6\\xee\\xdc]:\\xdb\\x16\\xed$\\xad\\xc7]PJP\\x06\\xa4\\x93\\xa0\\xaa\\x19\\xcf\\x11\\xcf\\xdd\\xa7\\xfc\\xa0\\xd9\\xb5/a\\xbb\\x04\\x12\\xf3\\xc2R\\xbcQ\\xe4\\xf8\\x12\\x7f\\x92A\\xd7\\xf6\\x8e]\\rv\\xc7\\x0fZ\\xc6Y\\xccx\\x9c\\xdf\\xe8\\xe6\\xfcO\\x10\\xbc\\xc51\\x07\\xafq+\\xa7\\xae\\xef\\x1eQS\\x8f<\\xa2\\xa5(\\xf5\\':\\xe8\\xe1r\\xb6\\xee\\xa2\\xf0\\xe1R\\x11:iLf\\x8b\\x9dJ@\\x9e&\\x82\\x13\\x96YEZ\\x01\\x15!\\x88\\x8d~4\\xa1\\x909{hCI\\xd4\\xc8\\xc8UL\\x15J\\x94\\x93\\x1c)\\x95B\\xdbW^\\x15U\\x07\\xbct\\x9c\\xa71O\\x81\\xbd\\x96\\x92g%U\\xb3\\xa3\\xb3\\x9ay\\xc7\\x1a\\xa0\\xd5\\xf4)$\\x82\\x92\\x99\\x04\\x1c\\xa0\\xe6\\x08\\xd3\\xd7\\xe5E\\xadN.\\xe3\\xa1\\xfb#\\xed\\xce\\x03\\x186\\xde\\xdc\\xa9I\\xc9\\xb61\\x85\\xe6G$\\xdcF\\xa3A\\xdek\\xfa@\\xeb\\\\r\\xe9\\xcb\\xe1\\xdb\\x0c\\xf7\\xe7\\xf5\\xfe?\\xb7\\xe7\\xd0\\xe8ZV\\x84\\xad\\xb5\\xa5m\\xa8\\x05%iPRT\\x08\\x90A\\x19\\x10G\\x1a\\xe4\\xeaQ5\\x00\\x06\\x08:\\xf1\\xa8\\xb0,k\\xb3\\xad\\xb1\\xbc\\xc4\\xefo\\\\\\xb2\\xb6}\\xcb\\x97\\x96\\xf2\\xbb\\xbb\\xc4\\x1fH\\x9c\\xb3\\x8e\\x10+\\xa4\\xea\\xc9\\xe63y\\xaag\\xbb>\\xda\\xb6}-\\x9f\\xbaP\\x1f\\xc5\\x94+\\xe0k\\x7f7\\x1a.\\xd0\\x1e\\xd9\\x9d\\xa1\\xb6\\x9e\\xf7\\x04\\xc5P:0\\xb3\\xf0\\xa7\\xe6c\\xee9@v\\xc6\\xf5\\x8c\\x9e\\xb3\\xbbn8\\xad\\x85\\x8f\\x88\\xa6e\\x8f\\xba\\xd23\\xa1hmR\\x95\\x05\\x04\\x93\\x1b\\xa4\\x1d)\\xee\\x94\\xc7U\\xec\\xb5\\xaf\\xccv_\\x08\\xb5\\x88\\xee\\xad\\x1bI\\x1dwA5\\xe4\\xf3[\\xab?*@\\xeaO\\x0b\\xda7j\\x1b9\\xb0\\xb6\\xeaM\\xf5\\xc0\\xbc\\xc5\\x14%\\xbc>\\xd9@\\xb8y\\x15\\x9d\\x10\\x9e\\xa7\\xd4\\rk\\x1c.B\\xd9\\x8f9W\\'\\xf6\\x8f\\xdan\\xd0\\xed\\xe5\\xc1N\\'q\\xf3|5*\\x96\\xb0\\xfbrCI\\xe4U\\xc5g\\xa9\\xf5\\x01]\\xb1\\xc6O\\x0e9\\xf5.\\\\N#\\xc3\\xfdh\\xe1[s\\t\\xcf\\x87\\xaa\\x84)\\xf6\\xf1\\x9aP\\x19\\x88\\xcaj\\x80S\\x19QP\\x8f>\\x15!\\xf59T\\x8a\\x00\\xe8\\x0eB\\xa4V\\xe4\\xfdZ\\xd2\\xd3\\xff\\xd9'"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["image_link.content"]}, {"cell_type": "code", "execution_count": 25, "id": "224ebe95", "metadata": {}, "outputs": [], "source": ["f = open('my_computer_image.jpg','wb')\n", "\n", "f.write(image_link.content)\n", "\n", "f.close()"]}, {"cell_type": "markdown", "id": "58286e64", "metadata": {}, "source": ["### Book Example"]}, {"cell_type": "code", "execution_count": 26, "id": "fbc7c63d", "metadata": {}, "outputs": [], "source": ["base_url = 'https://books.toscrape.com/catalogue/page-{}.html'"]}, {"cell_type": "code", "execution_count": 27, "id": "398d563d", "metadata": {}, "outputs": [{"data": {"text/plain": ["'https://books.toscrape.com/catalogue/page-1.html'"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["base_url.format('1')"]}, {"cell_type": "code", "execution_count": 28, "id": "fcfa6866", "metadata": {}, "outputs": [], "source": ["res = requests.get(base_url.format('1'))"]}, {"cell_type": "code", "execution_count": 29, "id": "138d8990", "metadata": {}, "outputs": [], "source": ["soup = bs4.BeautifulSoup(res.text,'lxml')"]}, {"cell_type": "code", "execution_count": 30, "id": "fa1af917", "metadata": {}, "outputs": [], "source": ["products = soup.select(\".product_pod\")"]}, {"cell_type": "code", "execution_count": 31, "id": "df9ecc4b", "metadata": {}, "outputs": [], "source": ["example = products[0]"]}, {"cell_type": "code", "execution_count": 32, "id": "86d18387", "metadata": {}, "outputs": [{"data": {"text/plain": ["<article class=\"product_pod\">\n", "<div class=\"image_container\">\n", "<a href=\"a-light-in-the-attic_1000/index.html\"><img alt=\"A Light in the Attic\" class=\"thumbnail\" src=\"../media/cache/2c/da/2cdad67c44b002e7ead0cc35693c0e8b.jpg\"/></a>\n", "</div>\n", "<p class=\"star-rating Three\">\n", "<i class=\"icon-star\"></i>\n", "<i class=\"icon-star\"></i>\n", "<i class=\"icon-star\"></i>\n", "<i class=\"icon-star\"></i>\n", "<i class=\"icon-star\"></i>\n", "</p>\n", "<h3><a href=\"a-light-in-the-attic_1000/index.html\" title=\"A Light in the Attic\">A Light in the ...</a></h3>\n", "<div class=\"product_price\">\n", "<p class=\"price_color\">Â£51.77</p>\n", "<p class=\"instock availability\">\n", "<i class=\"icon-ok\"></i>\n", "    \n", "        In stock\n", "    \n", "</p>\n", "<form>\n", "<button class=\"btn btn-primary btn-block\" data-loading-text=\"Adding...\" type=\"submit\">Add to basket</button>\n", "</form>\n", "</div>\n", "</article>"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["example"]}, {"cell_type": "code", "execution_count": 33, "id": "9e857b75", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["'star-rating Three' in str(example)"]}, {"cell_type": "code", "execution_count": 34, "id": "84a86171", "metadata": {}, "outputs": [{"data": {"text/plain": ["[<p class=\"star-rating Three\">\n", " <i class=\"icon-star\"></i>\n", " <i class=\"icon-star\"></i>\n", " <i class=\"icon-star\"></i>\n", " <i class=\"icon-star\"></i>\n", " <i class=\"icon-star\"></i>\n", " </p>]"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["example.select(\".star-rating.Three\")"]}, {"cell_type": "code", "execution_count": 35, "id": "71a124b0", "metadata": {}, "outputs": [{"data": {"text/plain": ["'A Light in the Attic'"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["example.select('a')[1]['title']"]}, {"cell_type": "code", "execution_count": 36, "id": "127ad568", "metadata": {}, "outputs": [], "source": ["two_star_titles = []\n", "\n", "for n in range(1,51):\n", "\n", "    scrape_url = base_url.format(n)\n", "    res = requests.get(scrape_url)\n", "\n", "    soup = bs4.BeautifulSoup(res.text,'lxml')\n", "    books = soup.select(\".product_pod\")\n", "\n", "    for book in books:\n", "        if len(book.select('.star-rating.Two')) != 0:\n", "            book_title = book.select('a')[1]['title']\n", "            two_star_titles.append(book_title)"]}, {"cell_type": "code", "execution_count": 37, "id": "699e1130", "metadata": {}, "outputs": [{"data": {"text/plain": ["['Starving Hearts (Triangular Trade Trilogy, #1)',\n", " 'Libertarianism for Beginners',\n", " \"It's Only the Himalayas\",\n", " 'How Music Works',\n", " '<PERSON><PERSON> (1883-1993):She Grew Up with the country',\n", " \"You can't bury them all: Poems\",\n", " 'Reasons to Stay Alive',\n", " 'Without Borders (Wanderlove #1)',\n", " 'Soul Reader',\n", " 'Security',\n", " 'Saga, Volume 5 (Saga (Collected Editions) #5)',\n", " 'Reskilling America: Learning to Labor in the Twenty-First Century',\n", " 'Political Suicide: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> Calls, Backroom Hijinx, Sordid Pasts, Rotten Breaks, and <PERSON>mb Mistakes in the Annals of American Politics',\n", " 'Obsidian (Lux #1)',\n", " 'My Paris Kitchen: Recipes and Stories',\n", " 'Masks and Shadows',\n", " '<PERSON><PERSON><PERSON><PERSON>, Vol. 2: Friendship to the Max (<PERSON>mber<PERSON><PERSON> #5-8)',\n", " 'Lumberjanes Vol. 3: A Terrible Plan (Lumberjanes #9-12)',\n", " 'Judo: Seven Steps to Black Belt (an Introductory Guide for Beginners)',\n", " 'I Hate Fairyland, Vol. 1: Madly Ever <PERSON> (I Hate Fairyland (Compilations) #1-5)',\n", " 'Giant Days, Vol. 2 (Giant Days #5-8)',\n", " 'Everydata: The Misinformation Hidden in the Little Data You Consume Every Day',\n", " \"Don't Be a Jerk: And Other Practical Advice from <PERSON><PERSON>, Japan's Greatest Zen Master\",\n", " 'Bossypants',\n", " 'Bitch Planet, Vol. 1: Extraordinary Machine (Bitch Planet (Collected Editions))',\n", " 'Avatar: The Last Airbender: <PERSON> and Shadow, Part 3 (Smoke and Shadow #3)',\n", " 'Tuesday Nights in 1980',\n", " 'The Psychopath Test: A Journey Through the Madness Industry',\n", " 'The Power of Now: A Guide to Spiritual Enlightenment',\n", " \"The Omnivore's Dilemma: A Natural History of Four Meals\",\n", " 'The Love and Lemons Cookbook: An Apple-to-Zucchini Celebration of Impromptu Cooking',\n", " 'The Girl on the Train',\n", " 'The Emerald Mystery',\n", " 'The Argonauts',\n", " 'Suddenly in Love (Lake Haven #1)',\n", " 'Soft Apocalypse',\n", " \"So You've Been Publicly Shamed\",\n", " 'Shoe Dog: A Memoir by the Creator of NIKE',\n", " 'Louisa: The Extraordinary Life of Mrs. <PERSON>',\n", " 'Large Print Heart of the Pride',\n", " 'Grumbles',\n", " 'Chasing Heaven: What Dying Taught Me About Living',\n", " 'Becoming Wise: An Inquiry into the Mystery and Art of Living',\n", " 'Beauty Restored (Riley Family Legacy Novellas #3)',\n", " 'Batman: The Long Halloween (<PERSON>)',\n", " \"<PERSON><PERSON><PERSON>'s Violin\",\n", " 'Wild Swans',\n", " \"What's It Like in Space?: Stories from Astronauts Who've Been There\",\n", " 'Until Friday Night (The Field Party #1)',\n", " 'Unbroken: A World War II Story of Survival, Resilience, and Redemption',\n", " 'Twenty Yawns',\n", " 'Through the Woods',\n", " 'This Is Where It Ends',\n", " 'The Year of Magical Thinking',\n", " 'The Last Mile (<PERSON> #2)',\n", " 'The Immortal Life of Henrietta Lacks',\n", " 'The Hidden Oracle (The Trials of Apollo #1)',\n", " 'The Guilty (<PERSON> #4)',\n", " 'Red Hood/Arsenal, Vol. 1: Open for Business (Red Hood/Arsenal #1)',\n", " 'Once Was a Time',\n", " 'No Dream Is Too High: Life Lessons From a Man Who Walked on the Moon',\n", " 'Na<PERSON><PERSON> (3-in-1 Edition), Vol. 14: Includes Vols. 40, 41 & 42 (Naruto: Omnibus #14)',\n", " 'More Than Music (Chasing the Dream #1)',\n", " 'Lowriders to the Center of the Earth (Lowriders in Space #2)',\n", " 'Eat Fat, Get Thin',\n", " '<PERSON> (The Shining #2)',\n", " 'Crazy Love: Overwhelmed by a Relentless God',\n", " 'Carrie',\n", " 'Batman: Europa',\n", " 'Angels Walking (Angels Walking #1)',\n", " 'Adulthood Is a Myth: A \"Sarah\\'s Scribbles\" Collection',\n", " 'A Study in Scarlet (<PERSON> #1)',\n", " 'A Series of Catastrophes and Miracles: A True Story of Love, Science, and Cancer',\n", " \"A People's History of the United States\",\n", " 'My Kitchen Year: 136 Recipes That Saved My Life',\n", " 'The Lonely City: Adventures in the Art of Being Alone',\n", " 'The Dinner Party',\n", " 'Stars Above (The Lunar Chronicles #4.5)',\n", " 'Love, Lies and Spies',\n", " 'Troublemaker: Surviving Hollywood and Scientology',\n", " 'The Widow',\n", " 'Setting the World on Fire: The Brief, Astonishing Life of St. Catherine of Siena',\n", " 'Mothering Sunday',\n", " 'Lilac Girls',\n", " '10% Happier: How I Tamed the Voice in My Head, Reduced Stress Without Losing My Edge, and Found Self-Help That Actually Works',\n", " 'Underlying Notes',\n", " 'The Flowers Lied',\n", " 'Modern Day Fables',\n", " \"Chernobyl 01:23:40: The Incredible True Story of the World's Worst Nuclear Disaster\",\n", " '23 Degrees South: A Tropical Tale of Changing Whether...',\n", " 'When Breath Becomes Air',\n", " 'Vagabonding: An Uncommon Guide to the Art of Long-Term World Travel',\n", " 'The Martian (The Martian #1)',\n", " \"Miller's Valley\",\n", " \"Love That Boy: What Two Presidents, Eight Road Trips, and My Son Taught Me About a Parent's Expectations\",\n", " 'Left Behind (Left Behind #1)',\n", " 'Howl and Other Poems',\n", " \"Heaven is for Real: A Little Boy's Astounding Story of His Trip to Heaven and Back\",\n", " \"Brazen: The Courage to Find the You That's Been Hiding\",\n", " '32 Yolks',\n", " 'Wildlife of New York: A Five-Borough Coloring Book',\n", " 'Unreasonable Hope: Finding Faith in the God Who Brings Purpose to Your Pain',\n", " 'The Art Book',\n", " 'Steal Like an Artist: 10 Things Nobody Told You About Being Creative',\n", " '<PERSON><PERSON>',\n", " 'Like Never Before (Walker Family #2)',\n", " 'How to Be a Domestic Goddess: Baking and the Art of Comfort Cooking',\n", " 'Finding God in the Ruins: How God Redeems Pain',\n", " 'Chronicles, Vol. 1',\n", " 'A Summer In Europe',\n", " 'The Rise and Fall of the Third Reich: A History of Nazi Germany',\n", " 'The Makings of a Fatherless Child',\n", " 'The Fellowship of the Ring (The Lord of the Rings #1)',\n", " \"Tell the Wolves I'm Home\",\n", " 'In the Woods (Dublin Murder Squad #1)',\n", " 'Give It Back',\n", " 'Why Save the Bankers?: And Other Essays on Our Economic and Political Crisis',\n", " 'The Raven King (The Raven Cycle #4)',\n", " 'The Expatriates',\n", " 'The 5th Wave (The 5th Wave #1)',\n", " 'Peak: Secrets from the New Science of Expertise',\n", " '<PERSON> (Fallen Crest High #5.5)',\n", " \"I Know Why the Caged Bird Sings (<PERSON>'s Autobiography #1)\",\n", " 'Drama',\n", " \"America's War for the Greater Middle East: A Military History\",\n", " 'A Game of Thrones (A Song of Ice and Fire #1)',\n", " \"The Pilgrim's Progress\",\n", " 'The Hound of the Baskervilles (<PERSON> #5)',\n", " \"The Geography of Bliss: One Grump's Search for the Happiest Places in the World\",\n", " 'The Demonists (Demonist #1)',\n", " 'The Demon Prince of Momochi House, Vol. 4 (The Demon Prince of Momochi House #4)',\n", " 'Misery',\n", " 'Far From True (Promise Falls Trilogy #2)',\n", " 'Confessions of a Shopaholic (Shopaholic #1)',\n", " 'Vegan Vegetarian Omnivore: Dinner for Everyone at the Table',\n", " 'Two Boys Kissing',\n", " 'Twilight (Twilight #1)',\n", " 'Twenties Girl',\n", " 'The Tipping Point: How Little Things Can Make a Big Difference',\n", " 'The Stand',\n", " 'The Picture of <PERSON>',\n", " 'The Name of God is Mercy',\n", " \"The Lover's Dictionary\",\n", " 'The Last Painting of <PERSON>',\n", " 'The Guns of August',\n", " 'The Girl Who Played with Fire (Millennium Trilogy #2)',\n", " 'The Da Vinci Code (<PERSON> #2)',\n", " 'The Cat in the Hat (Beginner Books B-1)',\n", " 'The Book Thief',\n", " 'The Autobiography of <PERSON>',\n", " \"Surely You're Joking, Mr. <PERSON><PERSON>!: Adventures of a Curious Character\",\n", " 'Soldier (Talon #3)',\n", " 'Shopaholic & Baby (Shopaholic #5)',\n", " 'Seven Days in the Art World',\n", " 'Rework',\n", " 'Packing for Mars: The Curious Science of Life in the Void',\n", " 'Orange Is the New Black',\n", " 'One for the Money (<PERSON> #1)',\n", " 'Midnight Riot (<PERSON>/ <PERSON> of London - books #1)',\n", " 'Me Talk Pretty One Day',\n", " 'Manuscript Found in Accra',\n", " 'Lust & Wonder',\n", " \"Life, the Universe and Everything (<PERSON><PERSON><PERSON><PERSON>'s Guide to the Galaxy #3)\",\n", " 'Life After Life',\n", " '<PERSON> <PERSON>: The Girl Who Stood Up for Education and Was Shot by the Taliban',\n", " 'House of Lost Worlds: Dinosaurs, Dynasties, and the Story of Life on Earth',\n", " 'Horrible Bear!',\n", " 'Holidays on Ice',\n", " 'Girl in the Blue Coat',\n", " 'Fruits Basket, Vol. 3 (Fruits Basket #3)',\n", " 'Cosmos',\n", " 'Civilization and Its Discontents',\n", " \"Catastrophic Happiness: Finding Joy in Childhood's Messy Years\",\n", " 'Career of Evil (<PERSON><PERSON>oran <PERSON> #3)',\n", " 'Born to Run: A Hidden Tribe, Superathletes, and the Greatest Race the World Has Never Seen',\n", " \"Best of My Love (Fool's Gold #20)\",\n", " '<PERSON><PERSON><PERSON>',\n", " 'Awkward',\n", " 'And Then There Were None',\n", " 'A Storm of Swords (A Song of Ice and Fire #3)',\n", " 'The Suffragettes (Little Black Classics, #96)',\n", " 'Vampire Girl (Vampire Girl #1)',\n", " 'Three Wishes (River of Time: California #1)',\n", " 'The Wicked + The Divine, Vol. 1: The Faust Act (The Wicked + The Divine)',\n", " 'The Little Prince',\n", " 'The Last Girl (The Dominion Trilogy #1)',\n", " 'Taking Shots (Assassins #1)',\n", " 'Settling the Score (The Summer Games #1)',\n", " 'Rhythm, Chord & Malykhin',\n", " 'One Second (Seven #7)',\n", " \"Old Records Never Die: One Man's Quest for His Vinyl and His Past\",\n", " 'Of Mice and Men',\n", " 'My Perfect Mistake (Over the Top #1)',\n", " 'Meditations',\n", " 'Frankenstein',\n", " 'Emma']"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["two_star_titles"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}