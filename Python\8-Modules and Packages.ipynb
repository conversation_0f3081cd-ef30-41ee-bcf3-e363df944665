{"cells": [{"cell_type": "markdown", "id": "53600861", "metadata": {}, "source": ["# Module 9: Mo<PERSON>les and Packages"]}, {"cell_type": "markdown", "id": "2af6cb87", "metadata": {}, "source": ["## Packages"]}, {"cell_type": "markdown", "id": "5099267e", "metadata": {}, "source": ["**`pip` and PyPI in Python**\n", "\n", "- **`pip`**: <PERSON>’s package installer. It is used to install and manage external libraries and packages.\n", "  - **Syntax**: `pip install package_name`\n", "\n", "- **PyPI (Python Package Index)**: A central repository where Python packages are published and shared. `pip` downloads packages from PyPI by default.\n", "\n", "Together, `pip` and PyPI make it easy to add third-party functionality to your Python projects.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "c9e850d1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Note: you may need to restart the kernel to use updated packages.\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["%pip install requests -q\n", "%pip install colorama -q"]}, {"cell_type": "code", "execution_count": 2, "id": "97170eb7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[31m<PERSON>ello World\n"]}], "source": ["from colorama import Fore\n", "\n", "print(Fore.RED + \"Hello World\")"]}, {"cell_type": "code", "execution_count": 3, "id": "74dc35b1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[32m<PERSON>ello World\n"]}], "source": ["print(Fore.GREEN + \"Hello World\")"]}, {"cell_type": "markdown", "id": "2346b620", "metadata": {}, "source": ["# __name__ and __main__  "]}, {"cell_type": "markdown", "id": "79d73f51", "metadata": {}, "source": ["**`__name__` and `__main__` in Python**\n", "\n", "In Python, every module has a built-in `__name__` variable.\n", "\n", "- When a file is run directly, `__name__` is set to `\"__main__\"`.\n", "- When a file is imported as a module, `__name__` is set to the module’s name.\n", "\n", "Using:\n", "\n", "```python\n", "if __name__ == \"__main__\":\n"]}, {"cell_type": "code", "execution_count": 12, "id": "61112b44", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["TOP LEVEL IN ONE.PY\n", "ONE.PY is being run directly!\n"]}], "source": ["#one.py\n", "\n", "def func():\n", "    print(\"FUNC() IN ONE.PY\")\n", "\n", "print(\"TOP LEVEL IN ONE.PY\")\n", "\n", "if __name__ == '__main__':\n", "    print('ONE.PY is being run directly!')\n", "else:\n", "    print('ONE.PY has been imported!')"]}, {"cell_type": "code", "execution_count": 13, "id": "356d7340", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["TOP LEVEL IN TWO.PY\n", "FUNC() IN ONE.PY\n", "TWO.PY is being run directly!\n"]}], "source": ["\n", "print(\"TOP LEVEL IN TWO.PY\")\n", "func()\n", "\n", "if __name__ == '__main__':\n", "    print('TWO.PY is being run directly!')\n", "else:\n", "    print('TWO.PY has been imported!')"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}