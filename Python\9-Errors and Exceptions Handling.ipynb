{"cells": [{"cell_type": "markdown", "id": "02381510", "metadata": {}, "source": ["# Module 10: Errors and Exceptions Handling"]}, {"cell_type": "code", "execution_count": 1, "id": "1137962c", "metadata": {}, "outputs": [], "source": ["def add(n1, n2):\n", "    print(n1 + n2)"]}, {"cell_type": "code", "execution_count": 2, "id": "96fc78e1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["30\n"]}], "source": ["add(10, 20)"]}, {"cell_type": "code", "execution_count": 3, "id": "439f39de", "metadata": {}, "outputs": [], "source": ["number1 = 10"]}, {"cell_type": "code", "execution_count": 4, "id": "edc793cf", "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "unsupported operand type(s) for +: 'int' and 'str'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mTypeError\u001b[0m                                 Trace<PERSON> (most recent call last)", "Cell \u001b[1;32mIn[4], line 3\u001b[0m\n\u001b[0;32m      1\u001b[0m number2 \u001b[38;5;241m=\u001b[39m \u001b[38;5;28minput\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mPlease provide a number: \u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m----> 3\u001b[0m add(number1, number2)\n", "Cell \u001b[1;32mIn[1], line 2\u001b[0m, in \u001b[0;36madd\u001b[1;34m(n1, n2)\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21madd\u001b[39m(n1, n2):\n\u001b[1;32m----> 2\u001b[0m     \u001b[38;5;28mprint\u001b[39m(n1 \u001b[38;5;241m+\u001b[39m n2)\n", "\u001b[1;31mTypeError\u001b[0m: unsupported operand type(s) for +: 'int' and 'str'"]}], "source": ["number2 = input(\"Please provide a number: \")\n", "\n", "add(number1, number2)"]}, {"cell_type": "markdown", "id": "4b1084e5", "metadata": {}, "source": ["**`try`, `except`, and `finally` in Python**\n", "\n", "- **`try`**: Defines a block of code to test for errors.\n", "- **`except`**: Catches exceptions (errors) that occur in the `try` block and handles them.\n", "- **`finally`**: Defines a block of code that will always execute, regardless of whether an exception occurred or not.\n", "\n", "These keywords are used for handling exceptions and ensuring cleanup operations are performed.\n"]}, {"cell_type": "code", "execution_count": null, "id": "6183d788", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Add went well!\n"]}], "source": ["try:\n", "    result = 10 + 10\n", "except:\n", "    print(\"Hey it looks like you aren't adding correctly!\")\n", "else:\n", "    print(\"Add went well!\")"]}, {"cell_type": "code", "execution_count": null, "id": "0a089eca", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hey it looks like you aren't adding correctly!\n"]}], "source": ["try:\n", "    result = 10 + \"10\"\n", "except:\n", "    print(\"Hey it looks like you aren't adding correctly!\")\n", "else:\n", "    print(\"Add went well!\")"]}, {"cell_type": "code", "execution_count": null, "id": "6d4e641c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["I always run\n"]}], "source": ["try:\n", "    f = open(\"testfile\", \"w\")\n", "    f.write(\"Write a test line\")\n", "except TypeError:\n", "    print(\"There was a type error!\")\n", "except OSError:\n", "    print(\"Hey you have an OS Error\")\n", "finally:\n", "    print(\"I always run\")"]}, {"cell_type": "code", "execution_count": null, "id": "1ea730ec", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hey you have an OS Error\n", "I always run\n"]}], "source": ["try:\n", "    f = open(\"testfile\", \"r\")\n", "    f.write(\"Write a test line\")\n", "except TypeError:\n", "    print(\"There was a type error!\")\n", "except OSError:\n", "    print(\"Hey you have an OS Error\")\n", "finally:\n", "    print(\"I always run\")"]}, {"cell_type": "code", "execution_count": null, "id": "0100bc54", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["All other exceptions\n", "I always run\n"]}], "source": ["try:\n", "    f = open(\"testfile\", \"r\")\n", "    f.write(\"Write a test line\")\n", "except TypeError:\n", "    print(\"There was a type error!\")\n", "except:\n", "    print(\"All other exceptions\")\n", "finally:\n", "    print(\"I always run\")"]}, {"cell_type": "code", "execution_count": null, "id": "9f59978f", "metadata": {}, "outputs": [], "source": ["def ask_for_int():\n", "    while True:\n", "        try:\n", "            result = int(input(\"Please provide number: \"))\n", "        except:\n", "            print(\"Whoops! That is not a number\")\n", "            continue\n", "        else:\n", "            print(\"Yes, thank you\")\n", "            break\n", "        finally:\n", "            print(\"End of try/except/finally\")"]}, {"cell_type": "code", "execution_count": null, "id": "ccd619d3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Yes, thank you\n", "End of try/except/finally\n"]}], "source": ["ask_for_int()"]}, {"cell_type": "code", "execution_count": null, "id": "ee1bee80", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["%pip install pylint -q"]}, {"cell_type": "markdown", "id": "c727212a", "metadata": {}, "source": ["**`pylint` in Python**\n", "\n", "`pylint` is a popular static code analysis tool used to check Python code for errors, enforce coding standards, and improve code quality.\n", "\n", "- It checks for issues like syntax errors, undefined variables, and incorrect function usage.\n", "- It also enforces coding style based on PEP 8 and provides a score indicating the quality of the code.\n", "\n", "You can install it using `pip`:\n", "\n", "```bash\n", "pip install pylint\n", "```\n", "\n", "To run pylint on a Python file:\n", "\n", "```bash\n", "pylint your_file.py"]}, {"cell_type": "markdown", "id": "ecf5e6d8", "metadata": {}, "source": ["**Output of Pylint**\n", "\n", "**Code:**\n", "\n", "a = 1 <br>\n", "b = 3 <br>\n", "print(a + b) <br>\n", "\n", "************* Module pylint <br>\n", "pylint.py:3:0: C0304: Final newline missing (missing-final-newline) <br>\n", "pylint.py:1:0: C0114: Missing module docstring (missing-module-docstring) <br>\n", "pylint.py:1:0: C0103: Constant name \"a\" doesn't conform to UPPER_CASE naming style (invalid-name) <br>\n", "pylint.py:2:0: C0103: Constant name \"b\" doesn't conform to UPPER_CASE naming style (invalid-name) <br>\n", "\n", "-----------------------------------\n", "Your code has been rated at 0.00/10"]}, {"cell_type": "markdown", "id": "50f0dc5a", "metadata": {}, "source": ["**Output of Pylint**\n", "\n", "**Code:**\n", "\n", "a = 1 <br>\n", "b = 3 <br>\n", "print(a + b) <br>\n", "\n", "************* <PERSON>dule pylint\n", "pylint.py:3:0: C0304: Final newline missing (missing-final-newline) <br>\n", "pylint.py:1:0: C0114: Missing module docstring (missing-module-docstring) <br>\n", "pylint.py:1:0: C0103: Constant name \"a\" doesn't conform to UPPER_CASE naming style (invalid-name) <br>\n", "pylint.py:2:0: C0103: Constant name \"b\" doesn't conform to UPPER_CASE naming style (invalid-name) <br>\n", "<span style=\"color: red;\">pylint.py:3:10: E0602: Undefined variable 'B' (undefined-variable)</span>\n", "\n", "------------------------------------------------------------------\n", "Your code has been rated at 0.00/10 (previous run: 0.00/10, +0.00)"]}, {"cell_type": "markdown", "id": "ec564647", "metadata": {}, "source": ["**Unit Testing in Python with `unittest`**\n", "\n", "`unittest` is Python's built-in framework for writing and running tests. It ensures that individual parts of your code (such as functions or methods) work as expected.\n", "\n", "- **`unittest.TestCase`**: The base class for creating test cases.\n", "- **Assertions**: Methods like `assertEqual()`, `assertTrue()`, and `assertRaises()` are used to check conditions in your tests.\n", "- **Test Runner**: `unittest.main()` runs all tests in a module when called.\n", "\n", "Unit testing helps verify the correctness of your code and is commonly used in test-driven development (TDD).\n"]}, {"cell_type": "code", "execution_count": null, "id": "c4869d4e", "metadata": {}, "outputs": [], "source": ["def cap_text(text):\n", "    return text.capitalize()"]}, {"cell_type": "code", "execution_count": null, "id": "7e162a63", "metadata": {}, "outputs": [], "source": ["import unittest\n", "\n", "class TestCap(unittest.TestCase):\n", "    \n", "    def test_one_word(self):\n", "        text = 'python'\n", "        result = cap_text(text)\n", "        self.assertEqual(result, 'Python')\n", "        \n", "    def test_multiple_words(self):\n", "        text = 'monty python'\n", "        result = cap_text(text)\n", "        self.assertEqual(result, 'Monty Python')\n", "\n", "if __name__ == '__main__':\n", "    unittest.main()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}