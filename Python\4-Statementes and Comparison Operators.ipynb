{"cells": [{"cell_type": "markdown", "id": "dd7a0527", "metadata": {}, "source": ["# Module 4 & 5"]}, {"cell_type": "markdown", "id": "a3723243-97b9-439a-be8b-0a7e53a3cd0f", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["## Comparison Operators"]}, {"cell_type": "markdown", "id": "09ebfce5-1160-4aa2-bbb2-81de4d08d336", "metadata": {}, "source": ["- Check Equality"]}, {"cell_type": "code", "execution_count": 1, "id": "b37f6617-b3d1-4214-835e-c0bd2585c0e7", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["2 == 2"]}, {"cell_type": "code", "execution_count": 2, "id": "01385880-249b-4a9e-bdb0-bb93d92ebcdf", "metadata": {}, "outputs": [{"data": {"text/plain": ["False"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["2 == 3"]}, {"cell_type": "code", "execution_count": 4, "id": "6b542494-d22e-4445-8f5f-8c8ef8cb5948", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["'Hello' == 'Hello'"]}, {"cell_type": "code", "execution_count": 5, "id": "3a8d202d-8264-46a6-9f1d-4b1ffebf1868", "metadata": {}, "outputs": [{"data": {"text/plain": ["False"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["'Hello' == 'Bye'"]}, {"cell_type": "code", "execution_count": 7, "id": "783e5fc5-e2ec-47bc-9425-29362ffd4954", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["2.0 == 2"]}, {"cell_type": "markdown", "id": "b4f3d220-cd5b-40c0-9d41-72fd289d8c07", "metadata": {}, "source": ["- Check Non Equality"]}, {"cell_type": "code", "execution_count": 8, "id": "a62c21a4-5810-4500-9e90-9995b31e70a4", "metadata": {}, "outputs": [{"data": {"text/plain": ["False"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["3 != 3"]}, {"cell_type": "code", "execution_count": 9, "id": "ce049937-a33d-4cc9-96a4-8a73730d5fb6", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["4 != 3"]}, {"cell_type": "markdown", "id": "4976cc87-e4c4-4187-9685-29b80a0b533a", "metadata": {}, "source": ["- Greater than & Less than"]}, {"cell_type": "code", "execution_count": 11, "id": "fa9f138a-3cde-48bf-b25c-b42f587ffc61", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["2 < 3"]}, {"cell_type": "code", "execution_count": 12, "id": "9f37df1b-2802-4c09-81b6-0d197c1875bd", "metadata": {}, "outputs": [{"data": {"text/plain": ["False"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["2 > 3"]}, {"cell_type": "code", "execution_count": 13, "id": "2134faa6-9d90-41d5-8ad7-58f63aa2f7fd", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["2 <= 2"]}, {"cell_type": "code", "execution_count": 14, "id": "98d6a769-3557-4d3d-92a9-cb0d12aeb649", "metadata": {}, "outputs": [{"data": {"text/plain": ["False"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["3 >= 5"]}, {"cell_type": "code", "execution_count": 15, "id": "6f6d1522-68f0-472e-bf45-0f39f1e42e8e", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["1 < 2 < 3"]}, {"cell_type": "code", "execution_count": 16, "id": "39e1b35e-f72a-4946-8170-134dc7fa30f5", "metadata": {}, "outputs": [{"data": {"text/plain": ["False"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["1 < 2 > 3"]}, {"cell_type": "code", "execution_count": 17, "id": "fd0ba873-5e27-4e05-bdc6-c7c8bfd288b5", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["1 < 4 > 3"]}, {"cell_type": "markdown", "id": "cf644c11-8ab2-4206-99b8-eb4efac62740", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["## Logical Operators"]}, {"cell_type": "markdown", "id": "e2ab7eaa-1a4a-4c4e-bc64-e4e73fa4683e", "metadata": {}, "source": ["**Logical Operators in Python**\n", "\n", "Logical operators in Python are used to combine conditional statements and return Boolean values (`True` or `False`). The three primary logical operators are:\n", "\n", "- **`and`**: Returns `True` if both operands are true.\n", "- **`or`**: Returns `True` if at least one of the operands is true.\n", "- **`not`**: Returns `True` if the operand is false, and vice versa.\n", "\n", "These operators are commonly used in control flow statements like `if`, `while`, and comprehensions to build complex logical conditions.\n"]}, {"cell_type": "code", "execution_count": 18, "id": "907db502-d668-4a14-b96b-992f7cf33428", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["2 == 2 and 3 == 3"]}, {"cell_type": "code", "execution_count": 19, "id": "1bb2be52-2655-4e1f-9dcd-0842b667582f", "metadata": {}, "outputs": [{"data": {"text/plain": ["False"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["2 == 3 and 3 == 3"]}, {"cell_type": "code", "execution_count": 21, "id": "1cd3757c-cde3-4a8d-b7b7-4c82f814442b", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["#This will work for different datatypes also. FOr Ex.\n", "'h' == 'h' and 3 == 3"]}, {"cell_type": "code", "execution_count": 22, "id": "e1421b19-e00c-4b97-8fad-232699c689ce", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["2 == 3 or 3 == 3"]}, {"cell_type": "code", "execution_count": 23, "id": "80477d26-64b7-4043-8def-fb2bfe950f7c", "metadata": {}, "outputs": [{"data": {"text/plain": ["False"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["2 == 3 and 3 == 4"]}, {"cell_type": "code", "execution_count": 24, "id": "be1d0e84-4513-4c7f-9abd-6a2dc6d6698c", "metadata": {}, "outputs": [{"data": {"text/plain": ["False"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["not 1 == 1"]}, {"cell_type": "code", "execution_count": 25, "id": "e80800db-10af-426b-a157-eaad66cc92aa", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["not 1 == 2"]}, {"cell_type": "code", "execution_count": 26, "id": "0d23c9b3-9dad-48c4-aa3e-e4e7c1020fc6", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["not 400 > 500"]}, {"cell_type": "markdown", "id": "afbc15bc-7c31-48ee-b9d7-fdb978d28897", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["## If, ELif and Else Statements"]}, {"cell_type": "code", "execution_count": 27, "id": "92c5fcc4-7547-4989-9b01-ce49505cce57", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Its True!\n"]}], "source": ["if  True:\n", "    print(\"Its True!\")"]}, {"cell_type": "code", "execution_count": 28, "id": "552fa2d0-5bc0-41db-8cff-40b26427ea2f", "metadata": {}, "outputs": [], "source": ["if False:\n", "    print(\"Its False!\")"]}, {"cell_type": "code", "execution_count": 31, "id": "690f2fcd-d791-447d-84de-6c7609632ae9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Feed Me!\n"]}], "source": ["hungry = True\n", "\n", "if hungry:\n", "    print(\"Feed Me!\")\n", "else:\n", "    print(\"I am not <PERSON><PERSON>!\")"]}, {"cell_type": "code", "execution_count": 32, "id": "ee1c521b-bd07-4fbd-a1a8-555ead1f9137", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["I am not <PERSON><PERSON>!\n"]}], "source": ["hungry = False\n", "\n", "if hungry:\n", "    print(\"Feed Me!\")\n", "else:\n", "    print(\"I am not <PERSON><PERSON>!\")"]}, {"cell_type": "code", "execution_count": 34, "id": "31b46acb-66f1-4d80-ba51-ae7d16451b31", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Money is Cool!\n"]}], "source": ["loc = 'Bank'\n", "\n", "if loc == 'Auto Shop':\n", "    print(\"Cars are Cool!\")\n", "elif loc == 'Bank':\n", "    print(\"Money is Cool!\")\n", "else:\n", "    print(\"I do not know much\")"]}, {"cell_type": "markdown", "id": "16c3ff7a-8944-4f35-9f30-7a9147a82b86", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["## For Loops & While Loops"]}, {"cell_type": "code", "execution_count": 35, "id": "904e7ca2-bdeb-462a-bcca-dbddbf0c5e16", "metadata": {}, "outputs": [], "source": ["mynum = [1,2,3,4,5,6,7,8,9,10]"]}, {"cell_type": "code", "execution_count": 36, "id": "9c789b8d-fa18-4bf0-b8ff-145d0b22501a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n", "2\n", "3\n", "4\n", "5\n", "6\n", "7\n", "8\n", "9\n", "10\n"]}], "source": ["for num in mynum:\n", "    print(num)"]}, {"cell_type": "code", "execution_count": 37, "id": "0ed39efc-a72b-47d8-a5c0-167b0cf96605", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Odd number: 1\n", "2\n", "Odd number: 3\n", "4\n", "Odd number: 5\n", "6\n", "Odd number: 7\n", "8\n", "Odd number: 9\n", "10\n"]}], "source": ["for num in mynum:\n", "    if num % 2 == 0:\n", "        print(num)\n", "    else:\n", "        print(f'Odd number: {num}')"]}, {"cell_type": "code", "execution_count": 38, "id": "f8d98351-af11-4f02-871b-c98720ee9389", "metadata": {}, "outputs": [{"data": {"text/plain": ["55"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["list_sum = 0\n", "\n", "for num in mynum:\n", "    list_sum = list_sum + num\n", "\n", "list_sum"]}, {"cell_type": "code", "execution_count": 39, "id": "ded4abba-41b3-46f8-941b-0b493fc9834c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["H\n", "e\n", "l\n", "l\n", "o\n", " \n", "W\n", "o\n", "r\n", "l\n", "d\n"]}], "source": ["mystring = \"Hello World\"\n", "\n", "for letter in mystring:\n", "    print(letter)"]}, {"cell_type": "code", "execution_count": 41, "id": "f2c47d10-5f5c-4ed2-8ad2-1e875772cac8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n", "2\n", "3\n"]}], "source": ["tup = (1,2,3)\n", "\n", "for item in tup:\n", "    print(item)"]}, {"cell_type": "code", "execution_count": 42, "id": "9af35985-64e9-41b6-a00b-105e3fa87ec1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(1, 2)\n", "(3, 4)\n", "(5, 6)\n", "(7, 8)\n"]}], "source": ["mylist = [(1,2),(3,4),(5,6),(7,8)]\n", "\n", "for item in mylist:\n", "    print(item)"]}, {"cell_type": "code", "execution_count": 43, "id": "168c2dec-41c1-44f3-a61f-6bcb2e8892b3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n", "2\n", "3\n", "4\n", "5\n", "6\n", "7\n", "8\n"]}], "source": ["for (a,b) in mylist:\n", "    print(a)\n", "    print(b)"]}, {"cell_type": "code", "execution_count": 45, "id": "5d4772da-9753-4d91-9a14-d2cd67a44f60", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["k1\n", "k2\n", "k3\n"]}], "source": ["d = {'k1':1,'k2':2,'k3':3}\n", "\n", "for item in d:\n", "    print(item)"]}, {"cell_type": "code", "execution_count": 46, "id": "936e28e6-5662-4cff-a6b2-0b3963755899", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["('k1', 1)\n", "('k2', 2)\n", "('k3', 3)\n"]}], "source": ["d = {'k1':1,'k2':2,'k3':3}\n", "\n", "for item in d.items():\n", "    print(item)"]}, {"cell_type": "code", "execution_count": 47, "id": "14471220-2924-438e-8d44-b5a4a1b48b64", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["k 1\n", "k 2\n", "k 3\n"]}], "source": ["d = {'k1':1,'k2':2,'k3':3}\n", "\n", "for key,values in d:\n", "    print(key,values)"]}, {"cell_type": "code", "execution_count": 50, "id": "49f0843c-7a08-4c63-a51b-adc5b3ef3e28", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["X = 0\n", "X = 1\n", "X = 2\n", "X = 3\n", "X = 4\n"]}], "source": ["x = 0\n", "while x < 5:\n", "    print(f'X = {x}')\n", "    x += 1"]}, {"cell_type": "markdown", "id": "119fb18e-c0dc-4aab-a50c-9e86541111a9", "metadata": {}, "source": ["**break, continue, pass**\n", "\n", "We can use `break`, `continue`, and `pass` statements in our loops to add additional functionality for various cases. The three statements are defined by:\n", "\n", "- `break`: Breaks out of the current closest enclosing loop.\n", "- `continue`: Goes to the top of the closest enclosing loop.\n", "- `pass`: Does nothing at all.\n"]}, {"cell_type": "code", "execution_count": 52, "id": "a89a3c56-b057-42e0-8f36-cf998509fe72", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["End of Script\n"]}], "source": ["#Pass statements\n", "for num in mynum:\n", "    pass\n", "print(\"End of Script\")"]}, {"cell_type": "code", "execution_count": 55, "id": "2ac5e6e5-a6f2-4f0b-b541-103e1a56699f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["S\n", "a\n", "y\n"]}], "source": ["mystring = '<PERSON>'\n", "\n", "for letter in mystring:\n", "    if letter == 'm':\n", "        continue\n", "    print(letter)"]}, {"cell_type": "code", "execution_count": 56, "id": "c4b4399f-b8f5-4f75-92f2-82ca42cac566", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["S\n", "a\n"]}], "source": ["for letter in mystring:\n", "    if letter == 'm':\n", "        break\n", "    print(letter)"]}, {"cell_type": "code", "execution_count": 57, "id": "ac0381b6-6938-4bf6-9591-e010fc2eb588", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["X = 0\n", "X = 1\n"]}], "source": ["x = 0\n", "while x < 5:\n", "\n", "    if x == 2:\n", "        break\n", "    \n", "    print(f'X = {x}')\n", "    x += 1"]}, {"cell_type": "markdown", "id": "adfaba47-f691-4903-8ac0-3deed5e072cc", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["## Useful operators in python"]}, {"cell_type": "markdown", "id": "4ace706c-3af8-4533-a919-4bf87fca3128", "metadata": {}, "source": ["**range in Python**\n", "\n", "The `range` function generates a sequence of numbers. It is commonly used for iterating over a sequence in loops. You can specify:\n", "\n", "- **start**: The value to start the sequence (default is 0).\n", "- **stop**: The value to stop the sequence (exclusive).\n", "- **step**: The value by which the sequence increments (default is 1).\n", "\n", "The sequence includes the **start** value but excludes the **stop** value."]}, {"cell_type": "code", "execution_count": 59, "id": "298b2ce9-7fb7-4d51-9a31-461f576ded3c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0\n", "1\n", "2\n", "3\n", "4\n"]}], "source": ["for i in range(5):\n", "    print(i)"]}, {"cell_type": "code", "execution_count": 65, "id": "6e9b1026-547f-4022-aebb-1d84744ee7f4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n", "2\n", "3\n", "4\n"]}], "source": ["for i in range(1,5):\n", "    print(i)\n", "\n", "#range work in start to stop-1. In this case 1 to 5-1=4"]}, {"cell_type": "code", "execution_count": 63, "id": "a1995bf5-3fac-4093-a228-beb9bebb850b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n", "3\n"]}], "source": ["for i in range(1,5,2): # it will take jump of two places instead of 1 as step's value is 2\n", "    print(i)"]}, {"cell_type": "code", "execution_count": 64, "id": "6a2483b7-c249-4b3d-bced-d88cf7fb1628", "metadata": {}, "outputs": [{"data": {"text/plain": ["[0, 2, 4, 6, 8, 10]"]}, "execution_count": 64, "metadata": {}, "output_type": "execute_result"}], "source": ["list(range(0,11,2))"]}, {"cell_type": "markdown", "id": "e39efb73-aa73-4167-b386-e7829b6db962", "metadata": {}, "source": ["**enumerate in Python**\n", "\n", "The `enumerate` function adds a counter to an iterable and returns it as an enumerate object. This is commonly used in loops to get both the index and the value of items in a sequence.\n", "\n", "- **iterable**: The sequence (like a list, tuple, or string) to iterate over.\n", "- **start**: The starting index (default is 0).\n", "\n", "The function returns an iterator of pairs, where the first element is the index and the second element is the corresponding item from the iterable.\n"]}, {"cell_type": "code", "execution_count": 66, "id": "a78555de-1adf-40ef-bddd-348adb6cf5be", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(0, 'a')\n", "(1, 'b')\n", "(2, 'c')\n", "(3, 'd')\n", "(4, 'e')\n"]}], "source": ["word = 'abcde'\n", "\n", "for item in enumerate(word):\n", "    print(item)"]}, {"cell_type": "markdown", "id": "57b3ac41-5b89-4897-aff8-0bb70822261f", "metadata": {}, "source": ["**zip in Python**\n", "\n", "The `zip` function takes multiple iterables (e.g., lists or tuples) and returns an iterator that aggregates elements from each iterable. It creates pairs of elements from the iterables, with the first elements paired together, the second elements paired together, and so on.\n", "\n", "- **iterables**: Two or more iterables to be combined.\n", "- The result is an iterator of tuples, where each tuple contains elements from the corresponding position in the input iterables.\n", "\n", "If the iterables are of unequal length, `zip` stops creating pairs when the shortest iterable is exhausted.\n"]}, {"cell_type": "code", "execution_count": 67, "id": "1467d333-30fd-4a4f-87c4-21dd3e84ef64", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(1, 'a', 100)\n", "(2, 'b', 200)\n", "(3, 'c', 300)\n"]}], "source": ["mylist1 = [1,2,3]\n", "mylist2 = ['a','b','c']\n", "mylist3 = [100,200,300]\n", "\n", "for item in zip(mylist1,mylist2,mylist3):\n", "    print(item)"]}, {"cell_type": "markdown", "id": "1798f1c9-814b-49d1-8c8a-11769063da9c", "metadata": {}, "source": ["**in in Python**\n", "\n", "The `in` keyword is used to check if a value exists within an iterable (e.g., a list, tuple, string, or dictionary). It returns `True` if the value is found, and `False` otherwise.\n", "\n", "- **Syntax**: `value in iterable`\n", "- It can also be used in loops to iterate over elements of an iterable.\n", "\n", "The `in` keyword is a quick way to check membership or perform iteration in Python.\n"]}, {"cell_type": "code", "execution_count": 68, "id": "3f9e92c2-e5e4-4a81-813b-760e46d3ce9f", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 68, "metadata": {}, "output_type": "execute_result"}], "source": ["2 in mylist1"]}, {"cell_type": "code", "execution_count": 70, "id": "3303697b-58e7-4ae4-9ca2-474369eb22e9", "metadata": {}, "outputs": [{"data": {"text/plain": ["False"]}, "execution_count": 70, "metadata": {}, "output_type": "execute_result"}], "source": ["4 in mylist1"]}, {"cell_type": "code", "execution_count": 71, "id": "a226d202-c466-484b-ba9d-406818086dbf", "metadata": {}, "outputs": [{"data": {"text/plain": ["100"]}, "execution_count": 71, "metadata": {}, "output_type": "execute_result"}], "source": ["min(mylist3)"]}, {"cell_type": "code", "execution_count": 72, "id": "b87294ba-c89e-4a41-808d-709bab93dcfc", "metadata": {}, "outputs": [{"data": {"text/plain": ["300"]}, "execution_count": 72, "metadata": {}, "output_type": "execute_result"}], "source": ["max(mylist3)"]}, {"cell_type": "markdown", "id": "ae87ab20-2f40-49cc-acbc-982070d4a1c3", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["## Some random functions"]}, {"cell_type": "code", "execution_count": 73, "id": "746cbccf-44d4-4ed1-9102-ffde099b4be5", "metadata": {}, "outputs": [], "source": ["from random import shuffle"]}, {"cell_type": "code", "execution_count": 78, "id": "e63fc8e7-0a3f-49e4-bc00-0ec8436426bf", "metadata": {}, "outputs": [], "source": ["mylist = list(range(11))"]}, {"cell_type": "code", "execution_count": 81, "id": "0907868f-a2fa-4cba-bf8c-2697fadf4ec3", "metadata": {}, "outputs": [{"data": {"text/plain": ["[4, 0, 8, 5, 2, 3, 10, 9, 1, 6, 7]"]}, "execution_count": 81, "metadata": {}, "output_type": "execute_result"}], "source": ["shuffle(mylist)\n", "mylist"]}, {"cell_type": "code", "execution_count": 83, "id": "bf235b8a-8301-4ab1-a134-cb74bd56c032", "metadata": {}, "outputs": [], "source": ["from random import randint"]}, {"cell_type": "code", "execution_count": 121, "id": "47e16470-d3e3-4f56-9dae-b4e3cae13a00", "metadata": {}, "outputs": [{"data": {"text/plain": ["3"]}, "execution_count": 121, "metadata": {}, "output_type": "execute_result"}], "source": ["randint(0,100)"]}, {"cell_type": "markdown", "id": "3e4f9f6d-a5b7-427f-8b23-8c350cab8bd3", "metadata": {}, "source": ["**input in Python**\n", "\n", "The `input` function allows the user to provide input via the console. It reads a line of text entered by the user and returns it as a string.\n", "\n", "- **Syntax**: `input(prompt)`\n", "  - **prompt**: An optional string that is displayed to the user before they enter their input.\n", "  \n", "The `input` function always returns the entered data as a string, even if the user enters a number.\n"]}, {"cell_type": "code", "execution_count": 126, "id": "655404a9-2bfd-45fd-a919-10d9d81f3b28", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["What is your name? MR\n"]}, {"data": {"text/plain": ["'MR'"]}, "execution_count": 126, "metadata": {}, "output_type": "execute_result"}], "source": ["name = input(\"What is your name?\")\n", "name"]}, {"cell_type": "code", "execution_count": 124, "id": "c003aa09-46a5-454f-b5c5-fb2705cc2d20", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Enter a number Here:  50\n"]}, {"data": {"text/plain": ["'50'"]}, "execution_count": 124, "metadata": {}, "output_type": "execute_result"}], "source": ["result = input(\"Enter a number Here: \")\n", "result"]}, {"cell_type": "code", "execution_count": 127, "id": "e693d6c0-fbe6-4730-aaed-de0a3535b2e5", "metadata": {}, "outputs": [{"data": {"text/plain": ["str"]}, "execution_count": 127, "metadata": {}, "output_type": "execute_result"}], "source": ["type(result)"]}, {"cell_type": "code", "execution_count": 128, "id": "b3bbcba2-4f4b-4be9-80fc-fd4cccafb90f", "metadata": {}, "outputs": [{"data": {"text/plain": ["50"]}, "execution_count": 128, "metadata": {}, "output_type": "execute_result"}], "source": ["int(result)"]}, {"cell_type": "code", "execution_count": 130, "id": "868b0fa5-7868-4768-88f9-dda2052d7943", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Enter a number Here:  50\n"]}, {"data": {"text/plain": ["50"]}, "execution_count": 130, "metadata": {}, "output_type": "execute_result"}], "source": ["#So you have to do this:\n", "result = int(input(\"Enter a number Here: \"))\n", "result"]}, {"cell_type": "code", "execution_count": 131, "id": "2d8785db-3317-4caf-8a65-510cf6a5a4ce", "metadata": {}, "outputs": [{"data": {"text/plain": ["int"]}, "execution_count": 131, "metadata": {}, "output_type": "execute_result"}], "source": ["type(result)"]}, {"cell_type": "markdown", "id": "536f7b61-8e0d-471e-87e6-75fa58e0cc6e", "metadata": {}, "source": ["## List Comprehensions"]}, {"cell_type": "markdown", "id": "9fedffb9-456a-4126-b430-c118f7b36810", "metadata": {}, "source": ["**List Comprehensions in Python**\n", "\n", "List comprehensions provide a concise way to create lists by performing operations on an existing iterable. It allows for compact syntax to generate a new list based on a condition or transformation.\n", "\n", "- **Syntax**: `[expression for item in iterable if condition]`\n", "  - **expression**: The operation or value to include in the new list.\n", "  - **item**: The variable representing each element from the iterable.\n", "  - **iterable**: The iterable (e.g., list, range) to iterate over.\n", "  - **condition** (optional): A condition that filters which items are included.\n", "\n", "List comprehensions can simplify code by replacing traditional for-loops with a more compact form.\n"]}, {"cell_type": "code", "execution_count": 141, "id": "08fd9d89-bf5f-49d1-9869-fedda5e3bd16", "metadata": {}, "outputs": [], "source": ["mystring = 'Hello World'\n", "\n", "mylist = []"]}, {"cell_type": "code", "execution_count": 142, "id": "f0c61a1d-6adb-4bd1-bb70-f4b034ad5fa1", "metadata": {}, "outputs": [{"data": {"text/plain": ["['H', 'e', 'l', 'l', 'o', ' ', 'W', 'o', 'r', 'l', 'd']"]}, "execution_count": 142, "metadata": {}, "output_type": "execute_result"}], "source": ["for letter in mystring:\n", "    mylist.append(letter)\n", "\n", "mylist\n", "#This is brute-force approach"]}, {"cell_type": "code", "execution_count": 152, "id": "d347fe93-c04b-493c-b168-4b30c1352617", "metadata": {}, "outputs": [{"data": {"text/plain": ["['H', 'e', 'l', 'l', 'o']"]}, "execution_count": 152, "metadata": {}, "output_type": "execute_result"}], "source": ["mystring = 'Hello'\n", "\n", "mylist = [letter for letter in mystring]\n", "mylist"]}, {"cell_type": "code", "execution_count": 154, "id": "d689c66a-7916-4071-b939-55bd533e22c7", "metadata": {}, "outputs": [{"data": {"text/plain": ["['w', 'o', 'r', 'd']"]}, "execution_count": 154, "metadata": {}, "output_type": "execute_result"}], "source": ["mylist = [x for x in 'word']\n", "mylist"]}, {"cell_type": "code", "execution_count": 159, "id": "5661672c-7688-4b4b-aa96-a6d7e47cb9b4", "metadata": {}, "outputs": [{"data": {"text/plain": ["[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10]"]}, "execution_count": 159, "metadata": {}, "output_type": "execute_result"}], "source": ["mylist = [num for num in range(11)]\n", "mylist"]}, {"cell_type": "code", "execution_count": null, "id": "294becc4-7a81-4b50-9003-514c22399967", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}