{"cells": [{"cell_type": "markdown", "id": "fa92be54", "metadata": {}, "source": ["# Module 12: Decora<PERSON>"]}, {"cell_type": "markdown", "id": "0dc3e630", "metadata": {}, "source": ["**Decorators in Python**\n", "\n", "A decorator is a function that modifies the behavior of another function or method. It allows you to add functionality to an existing function without modifying its structure.\n", "\n", "- **Syntax**: A decorator is applied using the `@decorator_name` syntax before the function definition.\n", "- Decorators are often used for logging, access control, memoization, and more.\n", "- They work by taking a function as input and returning a new function that adds or changes behavior.\n", "\n", "Decorators can be combined, and they are widely used in frameworks like Flask and Django to extend the behavior of views and routes.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "6e7edf59", "metadata": {}, "outputs": [], "source": ["def hello():\n", "    return \"Hello!\""]}, {"cell_type": "code", "execution_count": 2, "id": "37f4c98c", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Hello!'"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["hello()"]}, {"cell_type": "code", "execution_count": 3, "id": "d4a06104", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Hello!'"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["greet = hello\n", "greet()"]}, {"cell_type": "code", "execution_count": 4, "id": "f67cfbd3", "metadata": {}, "outputs": [], "source": ["del hello"]}, {"cell_type": "code", "execution_count": 5, "id": "131600a1", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'hello' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[5], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m hello()\n", "\u001b[1;31mNameError\u001b[0m: name 'hello' is not defined"]}], "source": ["hello()"]}, {"cell_type": "code", "execution_count": 6, "id": "82f62e47", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Hello!'"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["greet()"]}, {"cell_type": "code", "execution_count": 7, "id": "9263a414", "metadata": {}, "outputs": [], "source": ["def hello():\n", "    print('The Hello function  has been executed!')\n", "\n", "    def greet():\n", "        return ('\\t This is the greet() function inside hello!')\n", "    \n", "    def welcome():\n", "        return ('\\t This is welcome() inside Hello')\n", "    \n", "    print(greet())\n", "    print(welcome())\n", "    print('This is the end of the hello function!')"]}, {"cell_type": "code", "execution_count": 8, "id": "9a80adb8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The Hello function  has been executed!\n", "\t This is the greet() function inside hello!\n", "\t This is welcome() inside Hello\n", "This is the end of the hello function!\n"]}], "source": ["hello()"]}, {"cell_type": "code", "execution_count": 17, "id": "d289266d", "metadata": {}, "outputs": [], "source": ["def hello(name='<PERSON>'):\n", "    print('The Hello function  has been executed!')\n", "\n", "    def greet():\n", "        return '\\t This is the greet() function inside hello!'\n", "    \n", "    def welcome():\n", "        return '\\t This is welcome() inside Hello'\n", "    \n", "    if name == '<PERSON>':\n", "        return greet\n", "    else:\n", "        return welcome\n"]}, {"cell_type": "code", "execution_count": 18, "id": "3c836f4e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The Hello function  has been executed!\n"]}], "source": ["my_new_func = hello('<PERSON>')"]}, {"cell_type": "code", "execution_count": 19, "id": "cc8abe64", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\t This is the greet() function inside hello!\n"]}], "source": ["print(my_new_func() )"]}, {"cell_type": "code", "execution_count": 25, "id": "0f195914", "metadata": {}, "outputs": [], "source": ["def cool():\n", "\n", "    def super_cool():\n", "        return 'I am very cool!'\n", "    \n", "    return super_cool"]}, {"cell_type": "code", "execution_count": 26, "id": "26091eca", "metadata": {}, "outputs": [], "source": ["some_func = cool()"]}, {"cell_type": "code", "execution_count": 27, "id": "35e683f2", "metadata": {}, "outputs": [{"data": {"text/plain": ["'I am very cool!'"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["some_func()"]}, {"cell_type": "code", "execution_count": 29, "id": "272f67f3", "metadata": {}, "outputs": [], "source": ["def hello():\n", "    return 'Hi <PERSON>!'"]}, {"cell_type": "code", "execution_count": 31, "id": "1d8192b1", "metadata": {}, "outputs": [], "source": ["def other(some_def_func):\n", "    print('Other code runs here!')\n", "    print(some_def_func())"]}, {"cell_type": "code", "execution_count": 32, "id": "d3dba49e", "metadata": {}, "outputs": [{"data": {"text/plain": ["<function __main__.hello()>"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["hello"]}, {"cell_type": "code", "execution_count": 33, "id": "6fdae002", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Other code runs here!\n", "Hi <PERSON>!\n"]}], "source": ["other(hello)"]}, {"cell_type": "code", "execution_count": 34, "id": "1cd6c7e0", "metadata": {}, "outputs": [], "source": ["def new_decorator(original_func):\n", "\n", "    def wrap_func():\n", "\n", "        print('Some extra code, before the original function')\n", "\n", "        original_func()\n", "\n", "        print('Some extra code, after the original function')\n", "    \n", "    return wrap_func\n"]}, {"cell_type": "code", "execution_count": 35, "id": "9c02b97a", "metadata": {}, "outputs": [], "source": ["def func_needs_decorator():\n", "    print('I want to be decorated!!')"]}, {"cell_type": "code", "execution_count": 36, "id": "cb4cb497", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["I want to be decorated!!\n"]}], "source": ["func_needs_decorator()"]}, {"cell_type": "code", "execution_count": 37, "id": "bc26450d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Some extra code, before the original function\n", "I want to be decorated!!\n", "Some extra code, after the original function\n"]}], "source": ["decorated_func = new_decorator(func_needs_decorator)\n", "\n", "decorated_func()"]}, {"cell_type": "code", "execution_count": 38, "id": "9d4cfc58", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Some extra code, before the original function\n", "I want to be decorated!!\n", "Some extra code, after the original function\n"]}], "source": ["@new_decorator\n", "def func_needs_decorator():\n", "    print('I want to be decorated!!')\n", "\n", "func_needs_decorator()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}