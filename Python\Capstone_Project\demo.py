import pyautogui
import random
import time

keys = ['ctrl']

def random_key_press():
    return random.choice(keys)

def simulate_typing(duration=60):
    start_time = time.time()
    while True:
        key = random_key_press()
        
        if key == 'ctrl':
            pyautogui.keyDown('ctrl')
            time.sleep(random.uniform(0.05, 0.3))
            pyautogui.keyUp('ctrl')

        time.sleep(random.uniform(4, 7))
        
        if time.time() - start_time > duration:
            print(f"{duration} seconds have passed. Stopping...")
            break

if __name__ == "__main__":
    print("Starting to simulate random Ctrl key presses...")
    simulate_typing(6000)