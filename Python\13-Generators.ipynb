{"cells": [{"cell_type": "markdown", "id": "f367f7f9", "metadata": {}, "source": ["# Module 13: Generators"]}, {"cell_type": "markdown", "id": "61dac030", "metadata": {}, "source": ["**Generators in Python**\n", "\n", "Generators are special functions that return an iterable set of items, one at a time, using the `yield` keyword. They allow you to iterate over large datasets without storing them in memory, making them more memory-efficient than regular functions.\n", "\n", "- **`yield`**: Pauses the function and returns a value, but retains the state of the function to continue from where it left off when next called.\n", "- Generators are lazy, meaning they generate values only when needed.\n", "- They are useful for processing large files, streams, or sequences without loading everything into memory at once.\n", "\n", "Generators can be iterated over using `for` loops or manually with the `next()` function.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "f44305be", "metadata": {}, "outputs": [], "source": ["def create_cubes(n):\n", "\n", "    for i in range(n):\n", "        yield i**3"]}, {"cell_type": "code", "execution_count": 3, "id": "97daab9c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0\n", "1\n", "8\n", "27\n", "64\n", "125\n", "216\n", "343\n", "512\n", "729\n"]}], "source": ["for x in create_cubes(10):\n", "    print(x)"]}, {"cell_type": "code", "execution_count": 4, "id": "dc01d84e", "metadata": {}, "outputs": [{"data": {"text/plain": ["[0, 1, 8, 27, 64, 125, 216, 343, 512, 729]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["list(create_cubes(10))"]}, {"cell_type": "code", "execution_count": 5, "id": "1a40f884", "metadata": {}, "outputs": [], "source": ["def gen_fibon(n):\n", "\n", "    a = 1\n", "    b = 1\n", "    for i in range(10):\n", "        yield a\n", "        a, b = b, a+b"]}, {"cell_type": "code", "execution_count": 6, "id": "537a2865", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n", "1\n", "2\n", "3\n", "5\n", "8\n", "13\n", "21\n", "34\n", "55\n"]}], "source": ["for number in gen_fibon(10):\n", "    print(number)"]}, {"cell_type": "code", "execution_count": 15, "id": "689cff95", "metadata": {}, "outputs": [], "source": ["def simple_gen():\n", "    for x in range(3):\n", "        yield x"]}, {"cell_type": "code", "execution_count": 16, "id": "d6bce4b5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0\n", "1\n", "2\n"]}], "source": ["for number in simple_gen():\n", "    print(number)"]}, {"cell_type": "code", "execution_count": 17, "id": "83872458", "metadata": {}, "outputs": [], "source": ["g = simple_gen()"]}, {"cell_type": "code", "execution_count": 18, "id": "b731bb57", "metadata": {}, "outputs": [{"data": {"text/plain": ["<generator object simple_gen at 0x000002A730E41A80>"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["g"]}, {"cell_type": "code", "execution_count": 19, "id": "9aabb394", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0\n", "1\n", "2\n"]}], "source": ["print(next(g))\n", "print(next(g))\n", "print(next(g))"]}, {"cell_type": "code", "execution_count": 20, "id": "cdc60a81", "metadata": {}, "outputs": [{"ename": "StopIteration", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mStopIteration\u001b[0m                             Traceback (most recent call last)", "Cell \u001b[1;32mIn[20], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;28mnext\u001b[39m(g))\n", "\u001b[1;31mStopIteration\u001b[0m: "]}], "source": ["print(next(g))"]}, {"cell_type": "code", "execution_count": 21, "id": "5531e587", "metadata": {}, "outputs": [], "source": ["s = 'Hello'"]}, {"cell_type": "code", "execution_count": 22, "id": "b7c2b12a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["H\n", "e\n", "l\n", "l\n", "o\n"]}], "source": ["for letter in s:\n", "    print(letter)"]}, {"cell_type": "code", "execution_count": 23, "id": "0f3ed096", "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "'str' object is not an iterator", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mTypeError\u001b[0m                                 Trace<PERSON> (most recent call last)", "Cell \u001b[1;32mIn[23], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[38;5;28mnext\u001b[39m(s)\n", "\u001b[1;31mTypeError\u001b[0m: 'str' object is not an iterator"]}], "source": ["next(s)"]}, {"cell_type": "code", "execution_count": 24, "id": "0768ec0b", "metadata": {}, "outputs": [], "source": ["s_iter = iter(s)"]}, {"cell_type": "code", "execution_count": 25, "id": "7ec8a536", "metadata": {}, "outputs": [{"data": {"text/plain": ["'H'"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["next(s_iter)"]}, {"cell_type": "code", "execution_count": 26, "id": "3794f506", "metadata": {}, "outputs": [{"data": {"text/plain": ["'e'"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["next(s_iter)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}