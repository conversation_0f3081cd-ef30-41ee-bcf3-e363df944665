{"cells": [{"cell_type": "markdown", "id": "dda82f9b", "metadata": {}, "source": ["# Module 11: Mile Stone project 2"]}, {"cell_type": "code", "execution_count": 2, "id": "9ccf149c", "metadata": {}, "outputs": [], "source": ["# Card\n", "# Suit, Rank, Value"]}, {"cell_type": "markdown", "id": "c1f69dba", "metadata": {}, "source": ["## Card Class"]}, {"cell_type": "code", "execution_count": 3, "id": "9efeddb4", "metadata": {}, "outputs": [], "source": ["import random\n", "\n", "suits = ('Hearts', 'Diamonds', 'Spades', 'Clubs')\n", "ranks = ('Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine', 'Ten', '<PERSON>', 'Queen', 'King', '<PERSON>')\n", "values = {'Two':2, 'Three':3, 'Four':4, 'Five':5, 'Six':6, 'Seven':7, 'Eight':8, 'Nine':9, 'Ten':10, '<PERSON>':11, 'Queen':12, 'King':13, '<PERSON>':14}"]}, {"cell_type": "code", "execution_count": 4, "id": "3ab1861b", "metadata": {}, "outputs": [], "source": ["class Card:\n", "    def __init__(self, suit, rank):\n", "        self.suit = suit\n", "        self.rank = rank\n", "        self.value = values[rank]\n", "    \n", "    def __str__(self):\n", "        return self.rank + \" of \" + self.suit "]}, {"cell_type": "code", "execution_count": 5, "id": "8fa54f2b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Two of Hearts\n"]}], "source": ["two_hearts = Card(\"Hearts\", \"Two\")\n", "print(two_hearts)"]}, {"cell_type": "code", "execution_count": 6, "id": "ce3ff2e3", "metadata": {}, "outputs": [{"data": {"text/plain": ["('Hearts', 'Two')"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["two_hearts.suit, two_hearts.rank"]}, {"cell_type": "code", "execution_count": 7, "id": "8fe90fb0", "metadata": {}, "outputs": [], "source": ["three_of_clubs = Card(\"Clubs\", \"Three\")"]}, {"cell_type": "code", "execution_count": 8, "id": "b1362422", "metadata": {}, "outputs": [{"data": {"text/plain": ["3"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["three_of_clubs.value"]}, {"cell_type": "code", "execution_count": 9, "id": "b24833f3", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["two_hearts.value < three_of_clubs.value"]}, {"cell_type": "markdown", "id": "6786c7a1", "metadata": {}, "source": ["## Deck Class"]}, {"cell_type": "code", "execution_count": 10, "id": "6c5f1be8", "metadata": {}, "outputs": [], "source": ["class Deck:\n", "    def __init__(self):\n", "        self.all_cards = []\n", "        \n", "        for suit in suits:\n", "            for rank in ranks:\n", "                created_card = Card(suit, rank)\n", "                \n", "                self.all_cards.append(created_card)\n", "    \n", "    def shuffle(self):\n", "        random.shuffle(self.all_cards)\n", "    \n", "    def deal_one(self):\n", "        return self.all_cards.pop()"]}, {"cell_type": "code", "execution_count": 11, "id": "0996ab50", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Six of Hearts\n"]}, {"data": {"text/plain": ["51"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["new_deck = Deck()\n", "new_deck.shuffle()\n", "mycard = new_deck.deal_one()\n", "print(mycard)\n", "len(new_deck.all_cards)"]}, {"cell_type": "markdown", "id": "5858396b", "metadata": {}, "source": ["## Player Class"]}, {"cell_type": "code", "execution_count": 12, "id": "d516af6d", "metadata": {}, "outputs": [], "source": ["class Player:\n", "    def __init__(self, name):\n", "        self.name = name\n", "        self.all_cards = []\n", "    \n", "    def remove_one(self):\n", "        return self.all_cards.pop(0)\n", "    \n", "    def add_cards(self, new_cards):\n", "        if type(new_cards) == type([]):\n", "            self.all_cards.extend(new_cards)\n", "        else:\n", "            self.all_cards.append(new_cards)\n", "    \n", "    def __str__(self):\n", "        return f'Player {self.name} has {len(self.all_cards)} cards.'\n", "    \n", "    "]}, {"cell_type": "markdown", "id": "640ab2f2", "metadata": {}, "source": ["## Game Setup"]}, {"cell_type": "code", "execution_count": 13, "id": "a6935a34", "metadata": {}, "outputs": [], "source": ["#Game Setup\n", "player_one = Player(\"One\")\n", "player_two = Player(\"Two\")\n", "new_deck = Deck()\n", "new_deck.shuffle()\n", "\n", "for x in range(26):\n", "    player_one.add_cards(new_deck.deal_one())\n", "    player_two.add_cards(new_deck.deal_one())\n", "\n", "game_on = True"]}, {"cell_type": "code", "execution_count": 14, "id": "590b3d45", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Round 1\n", "Round 2\n", "Round 3\n", "Round 4\n", "Round 5\n", "Round 6\n", "Round 7\n", "Round 8\n", "Round 9\n", "Round 10\n", "Round 11\n", "Round 12\n", "Round 13\n", "Round 14\n", "Round 15\n", "Round 16\n", "Round 17\n", "Round 18\n", "Round 19\n", "Round 20\n", "Round 21\n", "Round 22\n", "Round 23\n", "Round 24\n", "Round 25\n", "Round 26\n", "Round 27\n", "Round 28\n", "Round 29\n", "Round 30\n", "Round 31\n", "Round 32\n", "Round 33\n", "Round 34\n", "Round 35\n", "Round 36\n", "Round 37\n", "Round 38\n", "Round 39\n", "Round 40\n", "Round 41\n", "Round 42\n", "Round 43\n", "Round 44\n", "Round 45\n", "Round 46\n", "Round 47\n", "Round 48\n", "Round 49\n", "Round 50\n", "Round 51\n", "Round 52\n", "Round 53\n", "Round 54\n", "Round 55\n", "Round 56\n", "Round 57\n", "Round 58\n", "Round 59\n", "Round 60\n", "Round 61\n", "Round 62\n", "Round 63\n", "Round 64\n", "Round 65\n", "Round 66\n", "Round 67\n", "Round 68\n", "Round 69\n", "Round 70\n", "Round 71\n", "Round 72\n", "Round 73\n", "Round 74\n", "Round 75\n", "Round 76\n", "Round 77\n", "Round 78\n", "Round 79\n", "Round 80\n", "Round 81\n", "Round 82\n", "Round 83\n", "Round 84\n", "Round 85\n", "Round 86\n", "Round 87\n", "Round 88\n", "Round 89\n", "Round 90\n", "Round 91\n", "Round 92\n", "Round 93\n", "Round 94\n", "Round 95\n", "Round 96\n", "Round 97\n", "Round 98\n", "Round 99\n", "Round 100\n", "Round 101\n", "Round 102\n", "Round 103\n", "Round 104\n", "Round 105\n", "Round 106\n", "Round 107\n", "Round 108\n", "Round 109\n", "Round 110\n", "Round 111\n", "Round 112\n", "Round 113\n", "Round 114\n", "WAR!\n", "Round 115\n", "Round 116\n", "Round 117\n", "Round 118\n", "Round 119\n", "Round 120\n", "Round 121\n", "Round 122\n", "Round 123\n", "Round 124\n", "Round 125\n", "Round 126\n", "Round 127\n", "Round 128\n", "Round 129\n", "Round 130\n", "Round 131\n", "Round 132\n", "Round 133\n", "Round 134\n", "Round 135\n", "Round 136\n", "Round 137\n", "Round 138\n", "Round 139\n", "Round 140\n", "Round 141\n", "Round 142\n", "Round 143\n", "Round 144\n", "Round 145\n", "Round 146\n", "Round 147\n", "Round 148\n", "Round 149\n", "Round 150\n", "Round 151\n", "Round 152\n", "Round 153\n", "Round 154\n", "Round 155\n", "Round 156\n", "Round 157\n", "Round 158\n", "Round 159\n", "Round 160\n", "Round 161\n", "Round 162\n", "Round 163\n", "Round 164\n", "Round 165\n", "Round 166\n", "Round 167\n", "Round 168\n", "Round 169\n", "Round 170\n", "Round 171\n", "Round 172\n", "Round 173\n", "WAR!\n", "Round 174\n", "Round 175\n", "Round 176\n", "Round 177\n", "Round 178\n", "Round 179\n", "Round 180\n", "Round 181\n", "Round 182\n", "Round 183\n", "Round 184\n", "Round 185\n", "Round 186\n", "Round 187\n", "Round 188\n", "Round 189\n", "Round 190\n", "Round 191\n", "Round 192\n", "Round 193\n", "Round 194\n", "Round 195\n", "Round 196\n", "Round 197\n", "Round 198\n", "Round 199\n", "Round 200\n", "Round 201\n", "Round 202\n", "Round 203\n", "Round 204\n", "Round 205\n", "WAR!\n", "Round 206\n", "Round 207\n", "Round 208\n", "WAR!\n", "Round 209\n", "Round 210\n", "Round 211\n", "Round 212\n", "Round 213\n", "Round 214\n", "Round 215\n", "Round 216\n", "Round 217\n", "WAR!\n", "Round 218\n", "Round 219\n", "Round 220\n", "Round 221\n", "Round 222\n", "Round 223\n", "Round 224\n", "Round 225\n", "Round 226\n", "Round 227\n", "Round 228\n", "Round 229\n", "Round 230\n", "Round 231\n", "Round 232\n", "WAR!\n", "Round 233\n", "Round 234\n", "Round 235\n", "Round 236\n", "Round 237\n", "Round 238\n", "Round 239\n", "WAR!\n", "Round 240\n", "Round 241\n", "Round 242\n", "Round 243\n", "WAR!\n", "Round 244\n", "Round 245\n", "Round 246\n", "Round 247\n", "Round 248\n", "Round 249\n", "Round 250\n", "Round 251\n", "Round 252\n", "Round 253\n", "Round 254\n", "Round 255\n", "Round 256\n", "Round 257\n", "Round 258\n", "Round 259\n", "Round 260\n", "Round 261\n", "Round 262\n", "Round 263\n", "Round 264\n", "Round 265\n", "Round 266\n", "Round 267\n", "Round 268\n", "Round 269\n", "Round 270\n", "WAR!\n", "Round 271\n", "Round 272\n", "Round 273\n", "Round 274\n", "Round 275\n", "Round 276\n", "Round 277\n", "WAR!\n", "Round 278\n", "Round 279\n", "Round 280\n", "Round 281\n", "Round 282\n", "Round 283\n", "Round 284\n", "Round 285\n", "Round 286\n", "WAR!\n", "Round 287\n", "Round 288\n", "Round 289\n", "Round 290\n", "Round 291\n", "Round 292\n", "Round 293\n", "Round 294\n", "Round 295\n", "Round 296\n", "Round 297\n", "Round 298\n", "Round 299\n", "Round 300\n", "Round 301\n", "Round 302\n", "Round 303\n", "WAR!\n", "Round 304\n", "Round 305\n", "Round 306\n", "Round 307\n", "Round 308\n", "Round 309\n", "Round 310\n", "Round 311\n", "Round 312\n", "WAR!\n", "Round 313\n", "Round 314\n", "Round 315\n", "Round 316\n", "Round 317\n", "Round 318\n", "Round 319\n", "Round 320\n", "Round 321\n", "Round 322\n", "WAR!\n", "Round 323\n", "Round 324\n", "Round 325\n", "Round 326\n", "Round 327\n", "Round 328\n", "Round 329\n", "Round 330\n", "Round 331\n", "Round 332\n", "Round 333\n", "Round 334\n", "WAR!\n", "Round 335\n", "Round 336\n", "Player Two out of cards! Game Over\n", "Player One Wins!\n"]}], "source": ["round_num = 0\n", "\n", "while game_on:\n", "    round_num += 1\n", "    print(f\"Round {round_num}\")\n", "    \n", "    if len(player_one.all_cards) == 0:\n", "        print(\"Player One out of cards! Game Over\")\n", "        print(\"Player Two Wins!\")\n", "        game_on = False\n", "        break\n", "        \n", "    if len(player_two.all_cards) == 0:\n", "        print(\"Player Two out of cards! Game Over\")\n", "        print(\"Player One Wins!\")\n", "        game_on = False\n", "        break\n", "\n", "    player_one_cards = []\n", "    player_one_cards.append(player_one.remove_one())\n", "    \n", "    player_two_cards = []\n", "    player_two_cards.append(player_two.remove_one())\n", "    \n", "    at_war = True\n", "    \n", "    while at_war:\n", "        if player_one_cards[-1].value > player_two_cards[-1].value:\n", "            player_one.add_cards(player_one_cards)\n", "            player_one.add_cards(player_two_cards)\n", "            at_war = False\n", "        elif player_one_cards[-1].value < player_two_cards[-1].value:\n", "            player_two.add_cards(player_one_cards)\n", "            player_two.add_cards(player_two_cards)\n", "            at_war = False\n", "        else:\n", "            print(\"WAR!\")\n", "            if len(player_one.all_cards) < 3:\n", "                print(\"Player One unable to declare war\")\n", "                print(\"Player Two Wins!\")\n", "                game_on = False\n", "                break\n", "            elif len(player_two.all_cards) < 3:\n", "                print(\"Player Two unable to declare war\")\n", "                print(\"Player One Wins!\")\n", "                game_on = False\n", "                break\n", "            else:\n", "                for num in range(3):\n", "                    player_one_cards.append(player_one.remove_one())\n", "                    player_two_cards.append(player_two.remove_one())"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}