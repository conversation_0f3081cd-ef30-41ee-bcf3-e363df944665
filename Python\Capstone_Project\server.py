from pyftpdlib.authorizers import DummyAuthorizer
from pyftpdlib.handlers import FTPHandler
from pyftpdlib.servers import FTPServer


def start_server():
    authorizer = DummyAuthorizer()
    authorizer.add_anonymous("Storage/", perm="elradfmw")

    handler = FTPHandler
    handler.authorizer = authorizer
    address = ("127.0.0.1", 21)

    server = FTPServer(address, handler)
    server.serve_forever()

if __name__ == '__main__':
    start_server()