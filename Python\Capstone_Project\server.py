from pyftpdlib.authorizers import Dummy<PERSON>uthorizer
from pyftpdlib.handlers import <PERSON>TPHandler
from pyftpdlib.servers import FTPServer
import signal
import sys
import threading
import tkinter as tk
from tkinter import ttk, messagebox


class FTPServerApp:
    def __init__(self):
        self.server = None
        self.server_thread = None
        self.running = False

        self.root = tk.Tk()
        self.root.title("FTP Server Control")
        self.root.geometry("400x300")
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        self.setup_ui()

    def setup_ui(self):
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        ttk.Label(main_frame, text="FTP Server Control", font=("Arial", 16, "bold")).pack(pady=(0, 20))

        self.status_label = ttk.Label(main_frame, text="Server Status: Not Running", foreground="red")
        self.status_label.pack(pady=(0, 20))

        self.start_button = ttk.Button(main_frame, text="Start Server", command=self.start_server)
        self.start_button.pack(fill=tk.X, pady=(0, 10))

        self.stop_button = ttk.Button(main_frame, text="Stop Server", command=self.stop_server, state=tk.DISABLED)
        self.stop_button.pack(fill=tk.X, pady=(0, 10))

        ttk.Separator(main_frame, orient=tk.HORIZONTAL).pack(fill=tk.X, pady=10)

        self.log_frame = ttk.LabelFrame(main_frame, text="Server Log")
        self.log_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))

        self.log_text = tk.Text(self.log_frame, height=8, state=tk.DISABLED)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def log_message(self, message):
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)

    def start_server(self):
        if self.running:
            return

        self.running = True
        self.server_thread = threading.Thread(target=self._run_server)
        self.server_thread.daemon = True
        self.server_thread.start()

        self.status_label.config(text="Server Status: Running", foreground="green")
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.log_message("FTP Server started on 127.0.0.1:21")

    def _run_server(self):
        try:
            authorizer = DummyAuthorizer()
            authorizer.add_anonymous("Storage/", perm="elradfmw")

            handler = FTPHandler
            handler.authorizer = authorizer
            address = ("127.0.0.1", 21)

            self.server = FTPServer(address, handler)
            self.server.serve_forever()
        except Exception as e:
            self.log_message(f"Error: {e}")
            self.running = False
            self.root.after(0, self._update_ui_stopped)

    def _update_ui_stopped(self):
        self.status_label.config(text="Server Status: Not Running", foreground="red")
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)

    def stop_server(self):
        if not self.running:
            return

        try:
            if self.server:
                self.server.close_all()
                self.log_message("FTP Server stopped")

            self.running = False
            self.status_label.config(text="Server Status: Not Running", foreground="red")
            self.start_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.DISABLED)
        except Exception as e:
            self.log_message(f"Error stopping server: {e}")

    def on_closing(self):
        if self.running:
            if messagebox.askyesno("Quit", "The server is still running. Do you want to stop it and exit?"):
                self.stop_server()
                self.root.destroy()
        else:
            self.root.destroy()

    def run(self):
        self.root.mainloop()


def handle_sigint(sig, frame):
    print("Shutting down FTP server...")
    sys.exit(0)


if __name__ == '__main__':
    signal.signal(signal.SIGINT, handle_sigint)
    app = FTPServerApp()
    app.run()