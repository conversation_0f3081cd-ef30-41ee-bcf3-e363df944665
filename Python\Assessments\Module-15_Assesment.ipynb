{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["___\n", "\n", "<a href='https://www.udemy.com/user/joseportilla/'><img src='../<PERSON>ian_Data_Logo.png'/></a>\n", "___\n", "<center><em>Content Copyright by Pierian Data</em></center>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Web Scraping Exercises \n", "\n", "## Complete the Tasks Below"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**TASK: Import any libraries you think you'll need to scrape a website.**"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# CODE HERE"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import requests\n", "import bs4"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**TASK: Use requests library and BeautifulSoup to connect to http://quotes.toscrape.com/ and get the HMTL text from the homepage.**"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# CODE HERE"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["res = requests.get(\"http://quotes.toscrape.com/\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["'<!DOCTYPE html>\\n<html lang=\"en\">\\n<head>\\n\\t<meta charset=\"UTF-8\">\\n\\t<title>Quotes to Scrape</title>\\n    <link rel=\"stylesheet\" href=\"/static/bootstrap.min.css\">\\n    <link rel=\"stylesheet\" href=\"/static/main.css\">\\n    \\n    \\n</head>\\n<body>\\n    <div class=\"container\">\\n        <div class=\"row header-box\">\\n            <div class=\"col-md-8\">\\n                <h1>\\n                    <a href=\"/\" style=\"text-decoration: none\">Quotes to Scrape</a>\\n                </h1>\\n            </div>\\n            <div class=\"col-md-4\">\\n                <p>\\n                \\n                    <a href=\"/login\">Login</a>\\n                \\n                </p>\\n            </div>\\n        </div>\\n    \\n\\n<div class=\"row\">\\n    <div class=\"col-md-8\">\\n\\n    <div class=\"quote\" itemscope itemtype=\"http://schema.org/CreativeWork\">\\n        <span class=\"text\" itemprop=\"text\">“The world as we have created it is a process of our thinking. It cannot be changed without changing our thinking.”</span>\\n        <span>by <small class=\"author\" itemprop=\"author\">Albert Einstein</small>\\n        <a href=\"/author/Albert-Einstein\">(about)</a>\\n        </span>\\n        <div class=\"tags\">\\n            Tags:\\n            <meta class=\"keywords\" itemprop=\"keywords\" content=\"change,deep-thoughts,thinking,world\" /    > \\n            \\n            <a class=\"tag\" href=\"/tag/change/page/1/\">change</a>\\n            \\n            <a class=\"tag\" href=\"/tag/deep-thoughts/page/1/\">deep-thoughts</a>\\n            \\n            <a class=\"tag\" href=\"/tag/thinking/page/1/\">thinking</a>\\n            \\n            <a class=\"tag\" href=\"/tag/world/page/1/\">world</a>\\n            \\n        </div>\\n    </div>\\n\\n    <div class=\"quote\" itemscope itemtype=\"http://schema.org/CreativeWork\">\\n        <span class=\"text\" itemprop=\"text\">“It is our choices, Harry, that show what we truly are, far more than our abilities.”</span>\\n        <span>by <small class=\"author\" itemprop=\"author\">J.K. Rowling</small>\\n        <a href=\"/author/J-K-Rowling\">(about)</a>\\n        </span>\\n        <div class=\"tags\">\\n            Tags:\\n            <meta class=\"keywords\" itemprop=\"keywords\" content=\"abilities,choices\" /    > \\n            \\n            <a class=\"tag\" href=\"/tag/abilities/page/1/\">abilities</a>\\n            \\n            <a class=\"tag\" href=\"/tag/choices/page/1/\">choices</a>\\n            \\n        </div>\\n    </div>\\n\\n    <div class=\"quote\" itemscope itemtype=\"http://schema.org/CreativeWork\">\\n        <span class=\"text\" itemprop=\"text\">“There are only two ways to live your life. One is as though nothing is a miracle. The other is as though everything is a miracle.”</span>\\n        <span>by <small class=\"author\" itemprop=\"author\">Albert Einstein</small>\\n        <a href=\"/author/Albert-Einstein\">(about)</a>\\n        </span>\\n        <div class=\"tags\">\\n            Tags:\\n            <meta class=\"keywords\" itemprop=\"keywords\" content=\"inspirational,life,live,miracle,miracles\" /    > \\n            \\n            <a class=\"tag\" href=\"/tag/inspirational/page/1/\">inspirational</a>\\n            \\n            <a class=\"tag\" href=\"/tag/life/page/1/\">life</a>\\n            \\n            <a class=\"tag\" href=\"/tag/live/page/1/\">live</a>\\n            \\n            <a class=\"tag\" href=\"/tag/miracle/page/1/\">miracle</a>\\n            \\n            <a class=\"tag\" href=\"/tag/miracles/page/1/\">miracles</a>\\n            \\n        </div>\\n    </div>\\n\\n    <div class=\"quote\" itemscope itemtype=\"http://schema.org/CreativeWork\">\\n        <span class=\"text\" itemprop=\"text\">“The person, be it gentleman or lady, who has not pleasure in a good novel, must be intolerably stupid.”</span>\\n        <span>by <small class=\"author\" itemprop=\"author\">Jane Austen</small>\\n        <a href=\"/author/Jane-Austen\">(about)</a>\\n        </span>\\n        <div class=\"tags\">\\n            Tags:\\n            <meta class=\"keywords\" itemprop=\"keywords\" content=\"aliteracy,books,classic,humor\" /    > \\n            \\n            <a class=\"tag\" href=\"/tag/aliteracy/page/1/\">aliteracy</a>\\n            \\n            <a class=\"tag\" href=\"/tag/books/page/1/\">books</a>\\n            \\n            <a class=\"tag\" href=\"/tag/classic/page/1/\">classic</a>\\n            \\n            <a class=\"tag\" href=\"/tag/humor/page/1/\">humor</a>\\n            \\n        </div>\\n    </div>\\n\\n    <div class=\"quote\" itemscope itemtype=\"http://schema.org/CreativeWork\">\\n        <span class=\"text\" itemprop=\"text\">“Imperfection is beauty, madness is genius and it&#39;s better to be absolutely ridiculous than absolutely boring.”</span>\\n        <span>by <small class=\"author\" itemprop=\"author\">Marilyn Monroe</small>\\n        <a href=\"/author/Marilyn-Monroe\">(about)</a>\\n        </span>\\n        <div class=\"tags\">\\n            Tags:\\n            <meta class=\"keywords\" itemprop=\"keywords\" content=\"be-yourself,inspirational\" /    > \\n            \\n            <a class=\"tag\" href=\"/tag/be-yourself/page/1/\">be-yourself</a>\\n            \\n            <a class=\"tag\" href=\"/tag/inspirational/page/1/\">inspirational</a>\\n            \\n        </div>\\n    </div>\\n\\n    <div class=\"quote\" itemscope itemtype=\"http://schema.org/CreativeWork\">\\n        <span class=\"text\" itemprop=\"text\">“Try not to become a man of success. Rather become a man of value.”</span>\\n        <span>by <small class=\"author\" itemprop=\"author\">Albert Einstein</small>\\n        <a href=\"/author/Albert-Einstein\">(about)</a>\\n        </span>\\n        <div class=\"tags\">\\n            Tags:\\n            <meta class=\"keywords\" itemprop=\"keywords\" content=\"adulthood,success,value\" /    > \\n            \\n            <a class=\"tag\" href=\"/tag/adulthood/page/1/\">adulthood</a>\\n            \\n            <a class=\"tag\" href=\"/tag/success/page/1/\">success</a>\\n            \\n            <a class=\"tag\" href=\"/tag/value/page/1/\">value</a>\\n            \\n        </div>\\n    </div>\\n\\n    <div class=\"quote\" itemscope itemtype=\"http://schema.org/CreativeWork\">\\n        <span class=\"text\" itemprop=\"text\">“It is better to be hated for what you are than to be loved for what you are not.”</span>\\n        <span>by <small class=\"author\" itemprop=\"author\">André Gide</small>\\n        <a href=\"/author/Andre-Gide\">(about)</a>\\n        </span>\\n        <div class=\"tags\">\\n            Tags:\\n            <meta class=\"keywords\" itemprop=\"keywords\" content=\"life,love\" /    > \\n            \\n            <a class=\"tag\" href=\"/tag/life/page/1/\">life</a>\\n            \\n            <a class=\"tag\" href=\"/tag/love/page/1/\">love</a>\\n            \\n        </div>\\n    </div>\\n\\n    <div class=\"quote\" itemscope itemtype=\"http://schema.org/CreativeWork\">\\n        <span class=\"text\" itemprop=\"text\">“I have not failed. I&#39;ve just found 10,000 ways that won&#39;t work.”</span>\\n        <span>by <small class=\"author\" itemprop=\"author\">Thomas A. Edison</small>\\n        <a href=\"/author/Thomas-A-Edison\">(about)</a>\\n        </span>\\n        <div class=\"tags\">\\n            Tags:\\n            <meta class=\"keywords\" itemprop=\"keywords\" content=\"edison,failure,inspirational,paraphrased\" /    > \\n            \\n            <a class=\"tag\" href=\"/tag/edison/page/1/\">edison</a>\\n            \\n            <a class=\"tag\" href=\"/tag/failure/page/1/\">failure</a>\\n            \\n            <a class=\"tag\" href=\"/tag/inspirational/page/1/\">inspirational</a>\\n            \\n            <a class=\"tag\" href=\"/tag/paraphrased/page/1/\">paraphrased</a>\\n            \\n        </div>\\n    </div>\\n\\n    <div class=\"quote\" itemscope itemtype=\"http://schema.org/CreativeWork\">\\n        <span class=\"text\" itemprop=\"text\">“A woman is like a tea bag; you never know how strong it is until it&#39;s in hot water.”</span>\\n        <span>by <small class=\"author\" itemprop=\"author\">Eleanor Roosevelt</small>\\n        <a href=\"/author/Eleanor-Roosevelt\">(about)</a>\\n        </span>\\n        <div class=\"tags\">\\n            Tags:\\n            <meta class=\"keywords\" itemprop=\"keywords\" content=\"misattributed-eleanor-roosevelt\" /    > \\n            \\n            <a class=\"tag\" href=\"/tag/misattributed-eleanor-roosevelt/page/1/\">misattributed-eleanor-roosevelt</a>\\n            \\n        </div>\\n    </div>\\n\\n    <div class=\"quote\" itemscope itemtype=\"http://schema.org/CreativeWork\">\\n        <span class=\"text\" itemprop=\"text\">“A day without sunshine is like, you know, night.”</span>\\n        <span>by <small class=\"author\" itemprop=\"author\">Steve Martin</small>\\n        <a href=\"/author/Steve-Martin\">(about)</a>\\n        </span>\\n        <div class=\"tags\">\\n            Tags:\\n            <meta class=\"keywords\" itemprop=\"keywords\" content=\"humor,obvious,simile\" /    > \\n            \\n            <a class=\"tag\" href=\"/tag/humor/page/1/\">humor</a>\\n            \\n            <a class=\"tag\" href=\"/tag/obvious/page/1/\">obvious</a>\\n            \\n            <a class=\"tag\" href=\"/tag/simile/page/1/\">simile</a>\\n            \\n        </div>\\n    </div>\\n\\n    <nav>\\n        <ul class=\"pager\">\\n            \\n            \\n            <li class=\"next\">\\n                <a href=\"/page/2/\">Next <span aria-hidden=\"true\">&rarr;</span></a>\\n            </li>\\n            \\n        </ul>\\n    </nav>\\n    </div>\\n    <div class=\"col-md-4 tags-box\">\\n        \\n            <h2>Top Ten tags</h2>\\n            \\n            <span class=\"tag-item\">\\n            <a class=\"tag\" style=\"font-size: 28px\" href=\"/tag/love/\">love</a>\\n            </span>\\n            \\n            <span class=\"tag-item\">\\n            <a class=\"tag\" style=\"font-size: 26px\" href=\"/tag/inspirational/\">inspirational</a>\\n            </span>\\n            \\n            <span class=\"tag-item\">\\n            <a class=\"tag\" style=\"font-size: 26px\" href=\"/tag/life/\">life</a>\\n            </span>\\n            \\n            <span class=\"tag-item\">\\n            <a class=\"tag\" style=\"font-size: 24px\" href=\"/tag/humor/\">humor</a>\\n            </span>\\n            \\n            <span class=\"tag-item\">\\n            <a class=\"tag\" style=\"font-size: 22px\" href=\"/tag/books/\">books</a>\\n            </span>\\n            \\n            <span class=\"tag-item\">\\n            <a class=\"tag\" style=\"font-size: 14px\" href=\"/tag/reading/\">reading</a>\\n            </span>\\n            \\n            <span class=\"tag-item\">\\n            <a class=\"tag\" style=\"font-size: 10px\" href=\"/tag/friendship/\">friendship</a>\\n            </span>\\n            \\n            <span class=\"tag-item\">\\n            <a class=\"tag\" style=\"font-size: 8px\" href=\"/tag/friends/\">friends</a>\\n            </span>\\n            \\n            <span class=\"tag-item\">\\n            <a class=\"tag\" style=\"font-size: 8px\" href=\"/tag/truth/\">truth</a>\\n            </span>\\n            \\n            <span class=\"tag-item\">\\n            <a class=\"tag\" style=\"font-size: 6px\" href=\"/tag/simile/\">simile</a>\\n            </span>\\n            \\n        \\n    </div>\\n</div>\\n\\n    </div>\\n    <footer class=\"footer\">\\n        <div class=\"container\">\\n            <p class=\"text-muted\">\\n                Quotes by: <a href=\"https://www.goodreads.com/quotes\">GoodReads.com</a>\\n            </p>\\n            <p class=\"copyright\">\\n                Made with <span class=\\'zyte\\'>❤</span> by <a class=\\'zyte\\' href=\"https://www.zyte.com\">Zyte</a>\\n            </p>\\n        </div>\\n    </footer>\\n</body>\\n</html>'"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["res.text"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**TASK: Get the names of all the authors on the first page.**"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["soup = bs4.BeautifulSoup(res.text,'lxml')"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["[<small class=\"author\" itemprop=\"author\"><PERSON></small>,\n", " <small class=\"author\" itemprop=\"author\"><PERSON><PERSON><PERSON><PERSON></small>,\n", " <small class=\"author\" itemprop=\"author\"><PERSON></small>,\n", " <small class=\"author\" itemprop=\"author\"><PERSON></small>,\n", " <small class=\"author\" itemprop=\"author\"><PERSON></small>,\n", " <small class=\"author\" itemprop=\"author\"><PERSON></small>,\n", " <small class=\"author\" itemprop=\"author\"><PERSON></small>,\n", " <small class=\"author\" itemprop=\"author\"><PERSON></small>,\n", " <small class=\"author\" itemprop=\"author\"><PERSON></small>,\n", " <small class=\"author\" itemprop=\"author\"><PERSON></small>]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["soup.select(\".author\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["authors = set() \n", "for name in soup.select(\".author\"):\n", "    authors.add(name.text)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'<PERSON>',\n", " '<PERSON>',\n", " '<PERSON>',\n", " '<PERSON><PERSON><PERSON><PERSON>',\n", " '<PERSON>',\n", " '<PERSON>',\n", " '<PERSON>',\n", " 'Thomas <PERSON>'}"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["authors"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**TASK: Create a list of all the quotes on the first page.**"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["quotes = []\n", "for quote in soup.select(\".text\"):\n", "    quotes.append(quote.text)#CODE HERE"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["['“The world as we have created it is a process of our thinking. It cannot be changed without changing our thinking.”',\n", " '“It is our choices, <PERSON>, that show what we truly are, far more than our abilities.”',\n", " '“There are only two ways to live your life. One is as though nothing is a miracle. The other is as though everything is a miracle.”',\n", " '“The person, be it gentleman or lady, who has not pleasure in a good novel, must be intolerably stupid.”',\n", " \"“Imperfection is beauty, madness is genius and it's better to be absolutely ridiculous than absolutely boring.”\",\n", " '“Try not to become a man of success. Rather become a man of value.”',\n", " '“It is better to be hated for what you are than to be loved for what you are not.”',\n", " \"“I have not failed. I've just found 10,000 ways that won't work.”\",\n", " \"“A woman is like a tea bag; you never know how strong it is until it's in hot water.”\",\n", " '“A day without sunshine is like, you know, night.”']"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["quotes"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**TASK: Inspect the site and use Beautiful Soup to extract the top ten tags from the requests text shown on the top right from the home page (e.g Love,Inspirational,Life, etc...). HINT: Keep in mind there are also tags underneath each quote, try to find a class only present in the top right tags, perhaps check the span.**"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["[<span class=\"tag-item\">\n", " <a class=\"tag\" href=\"/tag/love/\" style=\"font-size: 28px\">love</a>\n", " </span>,\n", " <span class=\"tag-item\">\n", " <a class=\"tag\" href=\"/tag/inspirational/\" style=\"font-size: 26px\">inspirational</a>\n", " </span>,\n", " <span class=\"tag-item\">\n", " <a class=\"tag\" href=\"/tag/life/\" style=\"font-size: 26px\">life</a>\n", " </span>,\n", " <span class=\"tag-item\">\n", " <a class=\"tag\" href=\"/tag/humor/\" style=\"font-size: 24px\">humor</a>\n", " </span>,\n", " <span class=\"tag-item\">\n", " <a class=\"tag\" href=\"/tag/books/\" style=\"font-size: 22px\">books</a>\n", " </span>,\n", " <span class=\"tag-item\">\n", " <a class=\"tag\" href=\"/tag/reading/\" style=\"font-size: 14px\">reading</a>\n", " </span>,\n", " <span class=\"tag-item\">\n", " <a class=\"tag\" href=\"/tag/friendship/\" style=\"font-size: 10px\">friendship</a>\n", " </span>,\n", " <span class=\"tag-item\">\n", " <a class=\"tag\" href=\"/tag/friends/\" style=\"font-size: 8px\">friends</a>\n", " </span>,\n", " <span class=\"tag-item\">\n", " <a class=\"tag\" href=\"/tag/truth/\" style=\"font-size: 8px\">truth</a>\n", " </span>,\n", " <span class=\"tag-item\">\n", " <a class=\"tag\" href=\"/tag/simile/\" style=\"font-size: 6px\">simile</a>\n", " </span>]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["soup.select('.tag-item')"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "love\n", "\n", "\n", "inspirational\n", "\n", "\n", "life\n", "\n", "\n", "humor\n", "\n", "\n", "books\n", "\n", "\n", "reading\n", "\n", "\n", "friendship\n", "\n", "\n", "friends\n", "\n", "\n", "truth\n", "\n", "\n", "simile\n", "\n"]}], "source": ["for item in soup.select(\".tag-item\"):\n", "    print(item.text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**TASK: Notice how there is more than one page, and subsequent pages look like this http://quotes.toscrape.com/page/2/. Use what you know about for loops and string concatenation to loop through all the pages and get all the unique authors on the website. Keep in mind there are many ways to achieve this, also note that you will need to somehow figure out how to check that your loop is on the last page with quotes. For debugging purposes, I will let you know that there are only 10 pages, so the last page is http://quotes.toscrape.com/page/10/, but try to create a loop that is robust enough that it wouldn't matter to know the amount of pages beforehand, perhaps use try/except for this, its up to you!**"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["url = 'http://quotes.toscrape.com/page/'"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["authors = set()\n", "\n", "for page in range(1,10):\n", "\n", "    # Concatenate to get new page URL\n", "    page_url = url+str(page)\n", "    # Obtain Request\n", "    res = requests.get(page_url)\n", "    # Turn into Soup\n", "    soup = bs4.BeautifulSoup(res.text,'lxml')\n", "    # Add Authors to our set\n", "    for name in soup.select(\".author\"):\n", "        authors.add(name.text)\n"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["<!DOCTYPE html>\n", "<html lang=\"en\">\n", "<head>\n", "<meta charset=\"utf-8\"/>\n", "<title>Quotes to <PERSON><PERSON><PERSON></title>\n", "<link href=\"/static/bootstrap.min.css\" rel=\"stylesheet\"/>\n", "<link href=\"/static/main.css\" rel=\"stylesheet\"/>\n", "</head>\n", "<body>\n", "<div class=\"container\">\n", "<div class=\"row header-box\">\n", "<div class=\"col-md-8\">\n", "<h1>\n", "<a href=\"/\" style=\"text-decoration: none\">Quotes to <PERSON><PERSON><PERSON></a>\n", "</h1>\n", "</div>\n", "<div class=\"col-md-4\">\n", "<p>\n", "<a href=\"/login\">Login</a>\n", "</p>\n", "</div>\n", "</div>\n", "<div class=\"row\">\n", "<div class=\"col-md-8\">\n", "<div class=\"quote\" itemscope=\"\" itemtype=\"http://schema.org/CreativeWork\">\n", "<span class=\"text\" itemprop=\"text\">“Anyone who has never made a mistake has never tried anything new.”</span>\n", "<span>by <small class=\"author\" itemprop=\"author\"><PERSON></small>\n", "<a href=\"/author/<PERSON>\">(about)</a>\n", "</span>\n", "<div class=\"tags\">\n", "            Tags:\n", "            <meta class=\"keywords\" content=\"mistakes\" itemprop=\"keywords\"/>\n", "<a class=\"tag\" href=\"/tag/mistakes/page/1/\">mistakes</a>\n", "</div>\n", "</div>\n", "<div class=\"quote\" itemscope=\"\" itemtype=\"http://schema.org/CreativeWork\">\n", "<span class=\"text\" itemprop=\"text\">“A lady's imagination is very rapid; it jumps from admiration to love, from love to matrimony in a moment.”</span>\n", "<span>by <small class=\"author\" itemprop=\"author\"><PERSON></small>\n", "<a href=\"/author/<PERSON><PERSON><PERSON>\">(about)</a>\n", "</span>\n", "<div class=\"tags\">\n", "            Tags:\n", "            <meta class=\"keywords\" content=\"humor,love,romantic,women\" itemprop=\"keywords\"/>\n", "<a class=\"tag\" href=\"/tag/humor/page/1/\">humor</a>\n", "<a class=\"tag\" href=\"/tag/love/page/1/\">love</a>\n", "<a class=\"tag\" href=\"/tag/romantic/page/1/\">romantic</a>\n", "<a class=\"tag\" href=\"/tag/women/page/1/\">women</a>\n", "</div>\n", "</div>\n", "<div class=\"quote\" itemscope=\"\" itemtype=\"http://schema.org/CreativeWork\">\n", "<span class=\"text\" itemprop=\"text\">“Remember, if the time should come when you have to make a choice between what is right and what is easy, remember what happened to a boy who was good, and kind, and brave, because he strayed across the path of Lord <PERSON>. Remember <PERSON><PERSON>.”</span>\n", "<span>by <small class=\"author\" itemprop=\"author\"><PERSON><PERSON><PERSON><PERSON></small>\n", "<a href=\"/author/<PERSON>-<PERSON>-<PERSON>\">(about)</a>\n", "</span>\n", "<div class=\"tags\">\n", "            Tags:\n", "            <meta class=\"keywords\" content=\"integrity\" itemprop=\"keywords\"/>\n", "<a class=\"tag\" href=\"/tag/integrity/page/1/\">integrity</a>\n", "</div>\n", "</div>\n", "<div class=\"quote\" itemscope=\"\" itemtype=\"http://schema.org/CreativeWork\">\n", "<span class=\"text\" itemprop=\"text\">“I declare after all there is no enjoyment like reading! How much sooner one tires of any thing than of a book! -- When I have a house of my own, I shall be miserable if I have not an excellent library.”</span>\n", "<span>by <small class=\"author\" itemprop=\"author\"><PERSON></small>\n", "<a href=\"/author/<PERSON><PERSON><PERSON>\">(about)</a>\n", "</span>\n", "<div class=\"tags\">\n", "            Tags:\n", "            <meta class=\"keywords\" content=\"books,library,reading\" itemprop=\"keywords\"/>\n", "<a class=\"tag\" href=\"/tag/books/page/1/\">books</a>\n", "<a class=\"tag\" href=\"/tag/library/page/1/\">library</a>\n", "<a class=\"tag\" href=\"/tag/reading/page/1/\">reading</a>\n", "</div>\n", "</div>\n", "<div class=\"quote\" itemscope=\"\" itemtype=\"http://schema.org/CreativeWork\">\n", "<span class=\"text\" itemprop=\"text\">“There are few people whom I really love, and still fewer of whom I think well. The more I see of the world, the more am I dissatisfied with it; and every day confirms my belief of the inconsistency of all human characters, and of the little dependence that can be placed on the appearance of merit or sense.”</span>\n", "<span>by <small class=\"author\" itemprop=\"author\"><PERSON></small>\n", "<a href=\"/author/<PERSON><PERSON><PERSON>\">(about)</a>\n", "</span>\n", "<div class=\"tags\">\n", "            Tags:\n", "            <meta class=\"keywords\" content=\"eliza<PERSON>-bennet,jane-austen\" itemprop=\"keywords\"/>\n", "<a class=\"tag\" href=\"/tag/elizabeth-bennet/page/1/\">elizabeth-bennet</a>\n", "<a class=\"tag\" href=\"/tag/jane-austen/page/1/\">jane-austen</a>\n", "</div>\n", "</div>\n", "<div class=\"quote\" itemscope=\"\" itemtype=\"http://schema.org/CreativeWork\">\n", "<span class=\"text\" itemprop=\"text\">“Some day you will be old enough to start reading fairy tales again.”</span>\n", "<span>by <small class=\"author\" itemprop=\"author\"><PERSON><PERSON><PERSON><PERSON></small>\n", "<a href=\"/author/<PERSON>-<PERSON><PERSON><PERSON>\">(about)</a>\n", "</span>\n", "<div class=\"tags\">\n", "            Tags:\n", "            <meta class=\"keywords\" content=\"age,fairytales,growing-up\" itemprop=\"keywords\"/>\n", "<a class=\"tag\" href=\"/tag/age/page/1/\">age</a>\n", "<a class=\"tag\" href=\"/tag/fairytales/page/1/\">fairytales</a>\n", "<a class=\"tag\" href=\"/tag/growing-up/page/1/\">growing-up</a>\n", "</div>\n", "</div>\n", "<div class=\"quote\" itemscope=\"\" itemtype=\"http://schema.org/CreativeWork\">\n", "<span class=\"text\" itemprop=\"text\">“We are not necessarily doubting that God will do the best for us; we are wondering how painful the best will turn out to be.”</span>\n", "<span>by <small class=\"author\" itemprop=\"author\"><PERSON><PERSON><PERSON><PERSON></small>\n", "<a href=\"/author/<PERSON>-<PERSON><PERSON><PERSON>\">(about)</a>\n", "</span>\n", "<div class=\"tags\">\n", "            Tags:\n", "            <meta class=\"keywords\" content=\"god\" itemprop=\"keywords\"/>\n", "<a class=\"tag\" href=\"/tag/god/page/1/\">god</a>\n", "</div>\n", "</div>\n", "<div class=\"quote\" itemscope=\"\" itemtype=\"http://schema.org/CreativeWork\">\n", "<span class=\"text\" itemprop=\"text\">“The fear of death follows from the fear of life. A man who lives fully is prepared to die at any time.”</span>\n", "<span>by <small class=\"author\" itemprop=\"author\"><PERSON></small>\n", "<a href=\"/author/<PERSON><PERSON><PERSON><PERSON>\">(about)</a>\n", "</span>\n", "<div class=\"tags\">\n", "            Tags:\n", "            <meta class=\"keywords\" content=\"death,life\" itemprop=\"keywords\"/>\n", "<a class=\"tag\" href=\"/tag/death/page/1/\">death</a>\n", "<a class=\"tag\" href=\"/tag/life/page/1/\">life</a>\n", "</div>\n", "</div>\n", "<div class=\"quote\" itemscope=\"\" itemtype=\"http://schema.org/CreativeWork\">\n", "<span class=\"text\" itemprop=\"text\">“A lie can travel half way around the world while the truth is putting on its shoes.”</span>\n", "<span>by <small class=\"author\" itemprop=\"author\"><PERSON></small>\n", "<a href=\"/author/<PERSON><PERSON><PERSON><PERSON>\">(about)</a>\n", "</span>\n", "<div class=\"tags\">\n", "            Tags:\n", "            <meta class=\"keywords\" content=\"misattributed-mark-twain,truth\" itemprop=\"keywords\"/>\n", "<a class=\"tag\" href=\"/tag/misattributed-mark-twain/page/1/\">misattributed-mark-twain</a>\n", "<a class=\"tag\" href=\"/tag/truth/page/1/\">truth</a>\n", "</div>\n", "</div>\n", "<div class=\"quote\" itemscope=\"\" itemtype=\"http://schema.org/CreativeWork\">\n", "<span class=\"text\" itemprop=\"text\">“I believe in Christianity as I believe that the sun has risen: not only because I see it, but because by it I see everything else.”</span>\n", "<span>by <small class=\"author\" itemprop=\"author\"><PERSON><PERSON><PERSON><PERSON></small>\n", "<a href=\"/author/<PERSON>-<PERSON><PERSON><PERSON>\">(about)</a>\n", "</span>\n", "<div class=\"tags\">\n", "            Tags:\n", "            <meta class=\"keywords\" content=\"christianity,faith,religion,sun\" itemprop=\"keywords\"/>\n", "<a class=\"tag\" href=\"/tag/christianity/page/1/\">christianity</a>\n", "<a class=\"tag\" href=\"/tag/faith/page/1/\">faith</a>\n", "<a class=\"tag\" href=\"/tag/religion/page/1/\">religion</a>\n", "<a class=\"tag\" href=\"/tag/sun/page/1/\">sun</a>\n", "</div>\n", "</div>\n", "<nav>\n", "<ul class=\"pager\">\n", "<li class=\"previous\">\n", "<a href=\"/page/8/\"><span aria-hidden=\"true\">←</span> Previous</a>\n", "</li>\n", "<li class=\"next\">\n", "<a href=\"/page/10/\">Next <span aria-hidden=\"true\">→</span></a>\n", "</li>\n", "</ul>\n", "</nav>\n", "</div>\n", "<div class=\"col-md-4 tags-box\">\n", "<h2>Top Ten tags</h2>\n", "<span class=\"tag-item\">\n", "<a class=\"tag\" href=\"/tag/love/\" style=\"font-size: 28px\">love</a>\n", "</span>\n", "<span class=\"tag-item\">\n", "<a class=\"tag\" href=\"/tag/inspirational/\" style=\"font-size: 26px\">inspirational</a>\n", "</span>\n", "<span class=\"tag-item\">\n", "<a class=\"tag\" href=\"/tag/life/\" style=\"font-size: 26px\">life</a>\n", "</span>\n", "<span class=\"tag-item\">\n", "<a class=\"tag\" href=\"/tag/humor/\" style=\"font-size: 24px\">humor</a>\n", "</span>\n", "<span class=\"tag-item\">\n", "<a class=\"tag\" href=\"/tag/books/\" style=\"font-size: 22px\">books</a>\n", "</span>\n", "<span class=\"tag-item\">\n", "<a class=\"tag\" href=\"/tag/reading/\" style=\"font-size: 14px\">reading</a>\n", "</span>\n", "<span class=\"tag-item\">\n", "<a class=\"tag\" href=\"/tag/friendship/\" style=\"font-size: 10px\">friendship</a>\n", "</span>\n", "<span class=\"tag-item\">\n", "<a class=\"tag\" href=\"/tag/friends/\" style=\"font-size: 8px\">friends</a>\n", "</span>\n", "<span class=\"tag-item\">\n", "<a class=\"tag\" href=\"/tag/truth/\" style=\"font-size: 8px\">truth</a>\n", "</span>\n", "<span class=\"tag-item\">\n", "<a class=\"tag\" href=\"/tag/simile/\" style=\"font-size: 6px\">simile</a>\n", "</span>\n", "</div>\n", "</div>\n", "</div>\n", "<footer class=\"footer\">\n", "<div class=\"container\">\n", "<p class=\"text-muted\">\n", "                Quotes by: <a href=\"https://www.goodreads.com/quotes\">GoodReads.com</a>\n", "</p>\n", "<p class=\"copyright\">\n", "                Made with <span class=\"zyte\">❤</span> by <a class=\"zyte\" href=\"https://www.zyte.com\">Zyte</a>\n", "</p>\n", "</div>\n", "</footer>\n", "</body>\n", "</html>"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["soup"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["page_still_valid = True\n", "authors = set()\n", "page = 1\n", "\n", "while page_still_valid:\n", "\n", "    # Concatenate to get new page URL\n", "    page_url = url+str(page)\n", "    \n", "    # Obtain Request\n", "    res = requests.get(page_url)\n", "    \n", "    # Check to see if we're on the last page\n", "    if \"No quotes found!\" in res.text:\n", "        break\n", "    \n", "    # Turn into Soup\n", "    soup = bs4.BeautifulSoup(res.text,'lxml')\n", "    \n", "    # Add Authors to our set\n", "    for name in soup.select(\".author\"):\n", "        authors.add(name.text)\n", "        \n", "    # Go to Next Page\n", "    page += 1"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'<PERSON>',\n", " '<PERSON> fils',\n", " '<PERSON>',\n", " '<PERSON>',\n", " '<PERSON>',\n", " '<PERSON><PERSON>',\n", " '<PERSON>',\n", " '<PERSON><PERSON><PERSON><PERSON>',\n", " '<PERSON>',\n", " '<PERSON>',\n", " '<PERSON>',\n", " '<PERSON><PERSON>',\n", " '<PERSON><PERSON><PERSON><PERSON>',\n", " '<PERSON>',\n", " '<PERSON><PERSON>',\n", " '<PERSON>',\n", " '<PERSON>',\n", " 'Garrison Keillor',\n", " '<PERSON>',\n", " '<PERSON>',\n", " '<PERSON>',\n", " '<PERSON>',\n", " 'Harper Lee',\n", " '<PERSON><PERSON><PERSON>',\n", " '<PERSON>',\n", " '<PERSON><PERSON><PERSON><PERSON>',\n", " '<PERSON><PERSON><PERSON><PERSON>',\n", " '<PERSON><PERSON><PERSON><PERSON>',\n", " '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\n", " '<PERSON>',\n", " '<PERSON>',\n", " '<PERSON>',\n", " '<PERSON><PERSON>',\n", " '<PERSON>',\n", " '<PERSON>',\n", " '<PERSON><PERSON><PERSON>',\n", " \"<PERSON>En<PERSON>\",\n", " '<PERSON>',\n", " '<PERSON>',\n", " '<PERSON>',\n", " '<PERSON>',\n", " '<PERSON>',\n", " '<PERSON>',\n", " '<PERSON><PERSON>',\n", " '<PERSON>',\n", " '<PERSON>',\n", " '<PERSON>',\n", " 'Thomas A<PERSON>',\n", " '<PERSON><PERSON><PERSON><PERSON>',\n", " '<PERSON>'}"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["authors"]}, {"cell_type": "markdown", "metadata": {}, "source": ["There are lots of other potential solutions that are even more robust and flexible, the main idea is the same though, use a while loop to cycle through potential pages and have a break condition based on the invalid page."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 4}