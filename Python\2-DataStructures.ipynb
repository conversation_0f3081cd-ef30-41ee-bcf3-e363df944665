{"cells": [{"cell_type": "markdown", "id": "8b3636bd", "metadata": {}, "source": ["# Module 3: Part 1"]}, {"cell_type": "markdown", "id": "795d53a7-3a3d-4b08-87aa-337d2c4df3ea", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["## List"]}, {"cell_type": "code", "execution_count": 1, "id": "cb744fd7-429c-4efa-9ce0-d1edd198cf02", "metadata": {}, "outputs": [], "source": ["my_list = [1,2,3]"]}, {"cell_type": "code", "execution_count": 2, "id": "c82dafcf-9cff-42d9-bb39-a802ec85f68a", "metadata": {}, "outputs": [{"data": {"text/plain": ["3"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["len(my_list)"]}, {"cell_type": "code", "execution_count": 3, "id": "d7194666-10ca-437c-a45a-59774a0464c3", "metadata": {}, "outputs": [{"data": {"text/plain": ["2"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["my_list[1]"]}, {"cell_type": "code", "execution_count": 4, "id": "adba4d48-96c8-4064-bdd7-938086f91ef1", "metadata": {}, "outputs": [{"data": {"text/plain": ["[2, 3]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["my_list[1:]"]}, {"cell_type": "code", "execution_count": 5, "id": "03e10561-05e9-4c77-b4e1-8f601a9459f4", "metadata": {}, "outputs": [{"data": {"text/plain": ["[1, 2, 3, 4, 5]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["another_list = [4,5]\n", "my_list + another_list"]}, {"cell_type": "code", "execution_count": 6, "id": "e107e59b-3417-41a0-9cd9-580f2ad09e4f", "metadata": {}, "outputs": [{"data": {"text/plain": ["[0, 2, 3]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["my_list[0] = 0\n", "my_list"]}, {"cell_type": "code", "execution_count": 7, "id": "990063e8-1806-475d-8286-b16b7f56decb", "metadata": {}, "outputs": [{"data": {"text/plain": ["[0, 2, 3, 4]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["my_list.append(4)\n", "my_list"]}, {"cell_type": "code", "execution_count": 8, "id": "7aca02bb-18fb-4045-923f-3829ff65ddc6", "metadata": {}, "outputs": [{"data": {"text/plain": ["4"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["my_list.pop()"]}, {"cell_type": "code", "execution_count": 9, "id": "e509f9a6-39a2-4b4c-92d0-cbd903f4f84f", "metadata": {}, "outputs": [], "source": ["new_list = [4,2,3,1]"]}, {"cell_type": "code", "execution_count": 10, "id": "a9f62ae5-7aff-49f1-bb22-c2d242aff779", "metadata": {}, "outputs": [], "source": ["new_list.sort()"]}, {"cell_type": "code", "execution_count": 11, "id": "eb9c0562-1a2f-456f-a7d8-6dd5049b5aae", "metadata": {}, "outputs": [{"data": {"text/plain": ["[1, 2, 3, 4]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["new_list"]}, {"cell_type": "code", "execution_count": 12, "id": "2f4d915c-9607-468f-bdc3-ffdade330f51", "metadata": {}, "outputs": [], "source": ["new_list.reverse()"]}, {"cell_type": "code", "execution_count": 13, "id": "6f92cd10-8746-45a7-a416-85c956377d4b", "metadata": {}, "outputs": [{"data": {"text/plain": ["[4, 3, 2, 1]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["new_list"]}, {"cell_type": "markdown", "id": "c2c4dcd7-02af-4f99-9907-48606decb336", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["## Dictionaries"]}, {"cell_type": "code", "execution_count": 14, "id": "be376644-d8e4-43ea-9ad6-5ab89f2fbc47", "metadata": {}, "outputs": [], "source": ["my_dict = {1:'Hello',2:'World'}"]}, {"cell_type": "code", "execution_count": 15, "id": "ce23979f-a611-40f6-9f63-c5c0ce74fbd6", "metadata": {}, "outputs": [{"data": {"text/plain": ["{1: 'Hello', 2: 'World'}"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["my_dict"]}, {"cell_type": "code", "execution_count": 16, "id": "adb9023c-5424-4ecd-8511-a844c8ec1598", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Hello'"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["my_dict[1]"]}, {"cell_type": "code", "execution_count": 17, "id": "08152405-68de-4728-a7cb-81d774205629", "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_keys([1, 2])"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["my_dict.keys()"]}, {"cell_type": "code", "execution_count": 18, "id": "737306a6-79c9-4202-a4ff-8f73f8235739", "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_values(['Hello', 'World'])"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["my_dict.values()"]}, {"cell_type": "code", "execution_count": 19, "id": "76505585-c265-4810-b328-5456e5faa2f5", "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_items([(1, 'Hello'), (2, 'World')])"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["my_dict.items()"]}, {"cell_type": "markdown", "id": "d1ef57cc-3977-47f7-ac46-2996b48590a4", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["## Tu<PERSON>"]}, {"cell_type": "code", "execution_count": 20, "id": "54421991-54ba-4c2d-8303-2fc66d0c4654", "metadata": {}, "outputs": [], "source": ["t = (1,2,3)"]}, {"cell_type": "code", "execution_count": 21, "id": "6e3e3493-c691-4aed-9452-1bb87f70a8a8", "metadata": {}, "outputs": [{"data": {"text/plain": ["tuple"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["type(t)"]}, {"cell_type": "code", "execution_count": 22, "id": "2df7d470-5c06-463a-8529-579cd2a5cda7", "metadata": {}, "outputs": [{"data": {"text/plain": ["3"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["len(t)"]}, {"cell_type": "code", "execution_count": 23, "id": "f9f85052-922e-4dff-a98b-607508784047", "metadata": {}, "outputs": [{"data": {"text/plain": ["1"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["t[0]"]}, {"cell_type": "code", "execution_count": 24, "id": "45bd1cfc-d773-4faa-a735-e2279f073b31", "metadata": {}, "outputs": [{"data": {"text/plain": ["3"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["t[-1]"]}, {"cell_type": "code", "execution_count": 25, "id": "23071f19-c22e-4db7-9d58-61a92d2485a6", "metadata": {}, "outputs": [], "source": ["t = ('a','a','b')"]}, {"cell_type": "code", "execution_count": 26, "id": "90096078-4633-4140-b9d6-96b9cffdd5d8", "metadata": {}, "outputs": [{"data": {"text/plain": ["2"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["t.count('a')"]}, {"cell_type": "code", "execution_count": 27, "id": "1641b1ca-9553-4e09-bd44-644d373baff5", "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["t.index('a')"]}, {"cell_type": "code", "execution_count": 29, "id": "f8765275-42f6-4518-9781-26e712cf2090", "metadata": {}, "outputs": [], "source": ["# t[0] = 1 # Will give an error as tuples are immutable"]}, {"cell_type": "markdown", "id": "7f44644a-fa2e-4870-a2a6-65e3c51fd8c8", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["## Sets"]}, {"cell_type": "code", "execution_count": 30, "id": "49b577b1-d214-4f22-9ed6-722949b7b823", "metadata": {}, "outputs": [], "source": ["myset = set()"]}, {"cell_type": "code", "execution_count": 32, "id": "5182f136-1b97-45bb-8588-493026b4efda", "metadata": {}, "outputs": [{"data": {"text/plain": ["set()"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["myset"]}, {"cell_type": "code", "execution_count": 33, "id": "09c6d51c-439b-4311-830c-ff5f34ee1e60", "metadata": {}, "outputs": [], "source": ["myset.add(1)"]}, {"cell_type": "code", "execution_count": 34, "id": "ad6aedce-2c92-46d7-812a-178cc119011b", "metadata": {}, "outputs": [], "source": ["myset.add(2)"]}, {"cell_type": "code", "execution_count": 35, "id": "36d9ac08-6fa2-439e-86b4-383ab419a58e", "metadata": {}, "outputs": [{"data": {"text/plain": ["{1, 2}"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["myset"]}, {"cell_type": "code", "execution_count": 36, "id": "4af47f9c-4933-471b-9867-6471e0739a2a", "metadata": {}, "outputs": [{"data": {"text/plain": ["{1, 2}"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["myset.add(2)\n", "myset"]}, {"cell_type": "code", "execution_count": 37, "id": "f22d3cf7-4fbb-4430-96da-402ee580ddf8", "metadata": {}, "outputs": [], "source": ["mylist = [1,1,1,1,1,2,2,2,2,2,3,3,3,3,3]"]}, {"cell_type": "code", "execution_count": 38, "id": "88e0afd9-c7c3-4f9e-aa77-d31456acb13c", "metadata": {}, "outputs": [{"data": {"text/plain": ["{1, 2, 3}"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["set(mylist)"]}, {"cell_type": "markdown", "id": "732d2833-caa1-4d10-a88a-651dac2d0b51", "metadata": {}, "source": ["## Booleans"]}, {"cell_type": "code", "execution_count": 39, "id": "31f305be-7021-46f0-b724-8387485b57e5", "metadata": {}, "outputs": [{"data": {"text/plain": ["bool"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["type(False)"]}, {"cell_type": "code", "execution_count": 40, "id": "a9b6faea-7197-4525-9cd0-4b55f5d94942", "metadata": {}, "outputs": [{"data": {"text/plain": ["False"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["1 > 2"]}, {"cell_type": "code", "execution_count": 41, "id": "34384441-8c8a-4516-865e-cf33b88e191d", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["1 == 1"]}, {"cell_type": "code", "execution_count": null, "id": "a748b7f8-18fb-4d75-9488-a6d587ddaa72", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}